[/Script/EngineSettings.GeneralProjectSettings]
ProjectID=A1B2C3D4E5F6789012345678901234567890ABCD
ProjectName=Space Game
ProjectVersion=1.0.0
SupportContact=
ProjectDisplayedTitle=NSLOCTEXT("[/Script/EngineSettings]", "GameName", "Space Game")
ProjectDebugTitleInfo=NSLOCTEXT("[/Script/EngineSettings]", "GameName", "Space Game")
bShouldWindowPreserveAspectRatio=True
bUseBorderlessWindow=False
bStartInVR=False
bAllowWindowResize=True
bAllowClose=True
bAllowMaximize=True
bAllowMinimize=True

[/Script/UnrealEd.ProjectPackagingSettings]
Build=IfProjectHasCode
BuildConfiguration=PPBC_Development
BuildTarget=
StagingDirectory=(Path="")
FullRebuild=False
ForDistribution=False
IncludeDebugFiles=False
BlueprintNativizationMethod=Disabled
bIncludeNativizedAssetsInProjectGeneration=False
bExcludeMonolithicEngineHeadersInNativizedCode=False
UsePakFile=True
bUseIoStore=True
bUseZenStore=False
bMakeBinaryConfig=False
bGenerateChunks=False
bGenerateNoChunks=False
bChunkHardReferencesOnly=False
bForceOneChunkPerFile=False
MaxChunkSize=0
BuildCanBeDistributed=False
+DirectoriesToAlwaysCook=(Path="/Game/Maps")
+DirectoriesToAlwaysCook=(Path="/Game/Core")
+DirectoriesToAlwaysCook=(Path="/Game/UI")
+DirectoriesToAlwaysCook=(Path="/Game/Audio")
+DirectoriesToAlwaysCook=(Path="/Game/Assets")
PerPlatformBuildConfig=()
PerPlatformTargetFlavorName=()
