#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "InteractableInterface.generated.h"

UINTERFACE(MinimalAPI, Blueprintable)
class UInteractableInterface : public UInterface
{
	GENERATED_BODY()
};

/**
 * Interface for objects that can be interacted with by the player
 */
class SPACEGAME_API IInteractableInterface
{
	GENERATED_BODY()

public:
	// Called when the player interacts with this object
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Interaction")
	void Interact(AActor* InteractingActor);
	virtual void Interact_Implementation(AActor* InteractingActor) {}

	// Called when the player enters interaction range
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Interaction")
	void OnInteractionEnter(AActor* InteractingActor);
	virtual void OnInteractionEnter_Implementation(AActor* InteractingActor) {}

	// Called when the player exits interaction range
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Interaction")
	void OnInteractionExit(AActor* InteractingActor);
	virtual void OnInteractionExit_Implementation(AActor* InteractingActor) {}

	// Returns whether this object can currently be interacted with
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Interaction")
	bool CanInteract(AActor* InteractingActor) const;
	virtual bool CanInteract_Implementation(AActor* InteractingActor) const { return true; }

	// Returns the interaction text to display to the player
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Interaction")
	FString GetInteractionText(AActor* InteractingActor) const;
	virtual FString GetInteractionText_Implementation(AActor* InteractingActor) const { return TEXT("Interact"); }

	// Returns the interaction range for this object
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Interaction")
	float GetInteractionRange() const;
	virtual float GetInteractionRange_Implementation() const { return 300.0f; }

	// Returns whether this interaction requires holding the key
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Interaction")
	bool RequiresHoldToInteract() const;
	virtual bool RequiresHoldToInteract_Implementation() const { return false; }

	// Returns the hold duration required for interaction (if RequiresHoldToInteract is true)
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Interaction")
	float GetHoldDuration() const;
	virtual float GetHoldDuration_Implementation() const { return 1.0f; }
};
