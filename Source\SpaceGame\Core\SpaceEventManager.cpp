#include "SpaceEventManager.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "SpaceGame.h"

void USpaceEventManager::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);
	
	if (bDebugMode)
	{
		UE_LOG(LogSpaceGame, Log, TEXT("SpaceEventManager initialized"));
	}
}

void USpaceEventManager::Deinitialize()
{
	EventSubscriptions.Empty();
	
	if (bDebugMode)
	{
		UE_LOG(LogSpaceGame, Log, TEXT("SpaceEventManager deinitialized"));
	}
	
	Super::Deinitialize();
}

USpaceEventManager* USpaceEventManager::GetInstance(const UObject* WorldContext)
{
	if (const UWorld* World = GEngine->GetWorldFromContextObject(WorldContext, EGetWorldErrorMode::LogAndReturnNull))
	{
		if (UGameInstance* GameInstance = World->GetGameInstance())
		{
			return GameInstance->GetSubsystem<USpaceEventManager>();
		}
	}
	return nullptr;
}

void USpaceEventManager::TriggerEvent(USpaceGameEvent* Event)
{
	if (!Event)
	{
		UE_LOG(LogSpaceGame, Warning, TEXT("Attempted to trigger null event"));
		return;
	}

	// Trigger generic event
	OnGenericEvent.Broadcast(Event);

	// Trigger specific event if subscribed
	if (EventSubscriptions.Contains(Event->EventName))
	{
		EventSubscriptions[Event->EventName].Broadcast(Event);
	}

	// Handle specific event types
	if (UPlayerHealthChangedEvent* HealthEvent = Cast<UPlayerHealthChangedEvent>(Event))
	{
		OnPlayerHealthChanged.Broadcast(HealthEvent);
	}
	else if (UPlayerHungerChangedEvent* HungerEvent = Cast<UPlayerHungerChangedEvent>(Event))
	{
		OnPlayerHungerChanged.Broadcast(HungerEvent);
	}
	else if (UPlayerOxygenChangedEvent* OxygenEvent = Cast<UPlayerOxygenChangedEvent>(Event))
	{
		OnPlayerOxygenChanged.Broadcast(OxygenEvent);
	}

	if (bDebugMode)
	{
		UE_LOG(LogSpaceGame, Log, TEXT("Event triggered: %s"), *Event->EventName);
	}
}

void USpaceEventManager::SubscribeToEvent(const FString& EventName, const FOnSpaceGameEvent& Delegate)
{
	if (!EventSubscriptions.Contains(EventName))
	{
		EventSubscriptions.Add(EventName, FOnSpaceGameEvent());
	}
	
	EventSubscriptions[EventName].AddDynamic(Delegate.GetUObject(), Delegate.GetFunctionName());
	
	if (bDebugMode)
	{
		UE_LOG(LogSpaceGame, Log, TEXT("Subscribed to event: %s"), *EventName);
	}
}

void USpaceEventManager::UnsubscribeFromEvent(const FString& EventName, const FOnSpaceGameEvent& Delegate)
{
	if (EventSubscriptions.Contains(EventName))
	{
		EventSubscriptions[EventName].RemoveDynamic(Delegate.GetUObject(), Delegate.GetFunctionName());
		
		if (bDebugMode)
		{
			UE_LOG(LogSpaceGame, Log, TEXT("Unsubscribed from event: %s"), *EventName);
		}
	}
}

void USpaceEventManager::TriggerPlayerHealthChanged(float CurrentHealth, float MaxHealth, float PreviousHealth)
{
	UPlayerHealthChangedEvent* Event = NewObject<UPlayerHealthChangedEvent>();
	Event->CurrentHealth = CurrentHealth;
	Event->MaxHealth = MaxHealth;
	Event->PreviousHealth = PreviousHealth;
	
	TriggerEvent(Event);
}

void USpaceEventManager::TriggerPlayerHungerChanged(float CurrentHunger, float MaxHunger, float PreviousHunger)
{
	UPlayerHungerChangedEvent* Event = NewObject<UPlayerHungerChangedEvent>();
	Event->CurrentHunger = CurrentHunger;
	Event->MaxHunger = MaxHunger;
	Event->PreviousHunger = PreviousHunger;
	
	TriggerEvent(Event);
}

void USpaceEventManager::TriggerPlayerOxygenChanged(float CurrentOxygen, float MaxOxygen, float PreviousOxygen)
{
	UPlayerOxygenChangedEvent* Event = NewObject<UPlayerOxygenChangedEvent>();
	Event->CurrentOxygen = CurrentOxygen;
	Event->MaxOxygen = MaxOxygen;
	Event->PreviousOxygen = PreviousOxygen;
	
	TriggerEvent(Event);
}
