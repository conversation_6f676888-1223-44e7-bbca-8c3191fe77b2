#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Engine/Engine.h"
#include "SpaceEventManager.generated.h"

// Base event class
UCLASS(BlueprintType, Blueprintable)
class SPACEGAME_API USpaceGameEvent : public UObject
{
	GENERATED_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = "Event")
	FString EventName;

	UPROPERTY(BlueprintReadWrite, Category = "Event")
	float Timestamp;

	USpaceGameEvent()
	{
		Timestamp = FPlatformTime::Seconds();
	}
};

// Player Health Changed Event
UCLASS(BlueprintType)
class SPACEGAME_API UPlayerHealthChangedEvent : public USpaceGameEvent
{
	GENERATED_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = "Health")
	float CurrentHealth;

	UPROPERTY(BlueprintReadWrite, Category = "Health")
	float MaxHealth;

	UPROPERTY(BlueprintReadWrite, Category = "Health")
	float PreviousHealth;

	UPlayerHealthChangedEvent()
	{
		EventName = TEXT("PlayerHealthChanged");
	}
};

// Player Hunger Changed Event
UCLASS(BlueprintType)
class SPACEGAME_API UPlayerHungerChangedEvent : public USpaceGameEvent
{
	GENERATED_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = "Hunger")
	float CurrentHunger;

	UPROPERTY(BlueprintReadWrite, Category = "Hunger")
	float MaxHunger;

	UPROPERTY(BlueprintReadWrite, Category = "Hunger")
	float PreviousHunger;

	UPlayerHungerChangedEvent()
	{
		EventName = TEXT("PlayerHungerChanged");
	}
};

// Player Oxygen Changed Event
UCLASS(BlueprintType)
class SPACEGAME_API UPlayerOxygenChangedEvent : public USpaceGameEvent
{
	GENERATED_BODY()

public:
	UPROPERTY(BlueprintReadWrite, Category = "Oxygen")
	float CurrentOxygen;

	UPROPERTY(BlueprintReadWrite, Category = "Oxygen")
	float MaxOxygen;

	UPROPERTY(BlueprintReadWrite, Category = "Oxygen")
	float PreviousOxygen;

	UPlayerOxygenChangedEvent()
	{
		EventName = TEXT("PlayerOxygenChanged");
	}
};

// Event delegate types
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnSpaceGameEvent, USpaceGameEvent*, Event);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPlayerHealthChanged, UPlayerHealthChangedEvent*, Event);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPlayerHungerChanged, UPlayerHungerChangedEvent*, Event);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPlayerOxygenChanged, UPlayerOxygenChangedEvent*, Event);

UCLASS(BlueprintType, Blueprintable)
class SPACEGAME_API USpaceEventManager : public UGameInstanceSubsystem
{
	GENERATED_BODY()

public:
	// Subsystem interface
	virtual void Initialize(FSubsystemCollectionBase& Collection) override;
	virtual void Deinitialize() override;

	// Get the singleton instance
	UFUNCTION(BlueprintPure, Category = "Event Manager", CallInEditor = true)
	static USpaceEventManager* GetInstance(const UObject* WorldContext);

	// Generic event system
	UFUNCTION(BlueprintCallable, Category = "Events")
	void TriggerEvent(USpaceGameEvent* Event);

	UFUNCTION(BlueprintCallable, Category = "Events")
	void SubscribeToEvent(const FString& EventName, const FOnSpaceGameEvent& Delegate);

	UFUNCTION(BlueprintCallable, Category = "Events")
	void UnsubscribeFromEvent(const FString& EventName, const FOnSpaceGameEvent& Delegate);

	// Specific event triggers
	UFUNCTION(BlueprintCallable, Category = "Player Events")
	void TriggerPlayerHealthChanged(float CurrentHealth, float MaxHealth, float PreviousHealth);

	UFUNCTION(BlueprintCallable, Category = "Player Events")
	void TriggerPlayerHungerChanged(float CurrentHunger, float MaxHunger, float PreviousHunger);

	UFUNCTION(BlueprintCallable, Category = "Player Events")
	void TriggerPlayerOxygenChanged(float CurrentOxygen, float MaxOxygen, float PreviousOxygen);

	// Event delegates
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnSpaceGameEvent OnGenericEvent;

	UPROPERTY(BlueprintAssignable, Category = "Player Events")
	FOnPlayerHealthChanged OnPlayerHealthChanged;

	UPROPERTY(BlueprintAssignable, Category = "Player Events")
	FOnPlayerHungerChanged OnPlayerHungerChanged;

	UPROPERTY(BlueprintAssignable, Category = "Player Events")
	FOnPlayerOxygenChanged OnPlayerOxygenChanged;

protected:
	UPROPERTY()
	TMap<FString, FOnSpaceGameEvent> EventSubscriptions;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
	bool bDebugMode = false;
};
