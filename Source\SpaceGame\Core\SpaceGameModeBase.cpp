#include "SpaceGameModeBase.h"
#include "Engine/Engine.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "GameFramework/PlayerController.h"
#include "SpaceGame.h"

ASpaceGameModeBase::ASpaceGameModeBase()
{
	PrimaryActorTick.bCanEverTick = true;
}

void ASpaceGameModeBase::BeginPlay()
{
	Super::BeginPlay();
	
	InitializeGame();
	SetGameState(EGameState::Menu);
}

void ASpaceGameModeBase::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	if (AutoSaveTimerHandle.IsValid())
	{
		GetWorldTimerManager().ClearTimer(AutoSaveTimerHandle);
	}
	
	Super::EndPlay(EndPlayReason);
}

void ASpaceGameModeBase::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
	
	if (IsInGame())
	{
		GameTime += DeltaTime;
		HandleGameInput();
	}
}

void ASpaceGameModeBase::InitializeGame()
{
	// Set up auto-save
	if (AutoSaveInterval > 0.0f)
	{
		StartAutoSave();
	}
	
	if (bDebugMode)
	{
		UE_LOG(LogSpaceGame, Log, TEXT("Game initialized"));
	}
}

void ASpaceGameModeBase::HandleGameInput()
{
	// Input handling is now done in PlayerController
	// This method can be used for game-wide input processing if needed
}

void ASpaceGameModeBase::SetGameState(EGameState NewState)
{
	if (CurrentGameState == NewState) return;

	PreviousGameState = CurrentGameState;
	CurrentGameState = NewState;

	// Handle cursor and time scale
	APlayerController* PC = GetWorld()->GetFirstPlayerController();
	if (PC)
	{
		switch (NewState)
		{
		case EGameState::Menu:
			UGameplayStatics::SetGamePaused(GetWorld(), false);
			PC->bShowMouseCursor = true;
			PC->SetInputMode(FInputModeUIOnly());
			break;

		case EGameState::Playing:
			UGameplayStatics::SetGamePaused(GetWorld(), false);
			PC->bShowMouseCursor = false;
			PC->SetInputMode(FInputModeGameOnly());
			break;

		case EGameState::Paused:
		case EGameState::GameOver:
		case EGameState::Inventory:
			UGameplayStatics::SetGamePaused(GetWorld(), true);
			PC->bShowMouseCursor = true;
			PC->SetInputMode(FInputModeUIOnly());
			break;

		case EGameState::Loading:
			UGameplayStatics::SetGamePaused(GetWorld(), false);
			PC->bShowMouseCursor = true;
			PC->SetInputMode(FInputModeUIOnly());
			break;
		}
	}

	// Broadcast state change
	OnGameStateChanged.Broadcast(NewState);

	if (bDebugMode)
	{
		UE_LOG(LogSpaceGame, Log, TEXT("Game state changed from %d to %d"), 
			(int32)PreviousGameState, (int32)CurrentGameState);
	}
}

void ASpaceGameModeBase::StartGame()
{
	LoadGameScene();
}

void ASpaceGameModeBase::PauseGame()
{
	if (CurrentGameState == EGameState::Playing)
		SetGameState(EGameState::Paused);
}

void ASpaceGameModeBase::ResumeGame()
{
	if (CurrentGameState == EGameState::Paused)
		SetGameState(EGameState::Playing);
}

void ASpaceGameModeBase::OpenInventory()
{
	if (CurrentGameState == EGameState::Playing)
		SetGameState(EGameState::Inventory);
}

void ASpaceGameModeBase::CloseInventory()
{
	if (CurrentGameState == EGameState::Inventory)
		SetGameState(EGameState::Playing);
}

void ASpaceGameModeBase::GameOver()
{
	SetGameState(EGameState::GameOver);
	SaveGame(); // Auto-save on game over
}

void ASpaceGameModeBase::ReturnToMenu()
{
	LoadMenuScene();
}

void ASpaceGameModeBase::QuitGame()
{
	SaveGame();
	UKismetSystemLibrary::QuitGame(GetWorld(), nullptr, EQuitPreference::Quit, false);
}

void ASpaceGameModeBase::LoadGameScene()
{
	SetGameState(EGameState::Loading);
	UGameplayStatics::OpenLevel(GetWorld(), FName(*GameLevelName));
}

void ASpaceGameModeBase::LoadMenuScene()
{
	SetGameState(EGameState::Loading);
	UGameplayStatics::OpenLevel(GetWorld(), FName(*MainMenuLevelName));
}

void ASpaceGameModeBase::StartAutoSave()
{
	GetWorldTimerManager().SetTimer(AutoSaveTimerHandle, this, &ASpaceGameModeBase::AutoSaveGame, 
		AutoSaveInterval, true);
}

void ASpaceGameModeBase::AutoSaveGame()
{
	if (IsInGame())
	{
		SaveGame();
		if (bDebugMode)
		{
			UE_LOG(LogSpaceGame, Log, TEXT("Auto-save completed"));
		}
	}
}

void ASpaceGameModeBase::SaveGame()
{
	// TODO: Implement save system using UE5's save game system
	// This will be expanded when we implement the save/load functionality
	
	if (bDebugMode)
	{
		UE_LOG(LogSpaceGame, Log, TEXT("Game saved"));
	}
}

void ASpaceGameModeBase::LoadGame()
{
	// TODO: Implement load system using UE5's save game system
	// This will be expanded when we implement the save/load functionality
	
	if (bDebugMode)
	{
		UE_LOG(LogSpaceGame, Log, TEXT("Game loaded"));
	}
}
