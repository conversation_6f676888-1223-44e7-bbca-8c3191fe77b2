#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameModeBase.h"
#include "Engine/Engine.h"
#include "SpaceGameModeBase.generated.h"

UENUM(BlueprintType)
enum class EGameState : uint8
{
	Menu		UMETA(DisplayName = "Menu"),
	Playing		UMETA(DisplayName = "Playing"),
	Paused		UMETA(DisplayName = "Paused"),
	GameOver	UMETA(DisplayName = "Game Over"),
	Loading		UMETA(DisplayName = "Loading"),
	Inventory	UMETA(DisplayName = "Inventory")
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnGameStateChanged, EGameState, NewGameState);

UCLASS()
class SPACEGAME_API ASpaceGameModeBase : public AGameModeBase
{
	GENERATED_BODY()

public:
	ASpaceGameModeBase();

protected:
	virtual void BeginPlay() override;
	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

public:
	virtual void Tick(float DeltaTime) override;

	// Game State Management
	UFUNCTION(BlueprintCallable, Category = "Game State")
	void SetGameState(EGameState NewState);

	UFUNCTION(BlueprintPure, Category = "Game State")
	EGameState GetCurrentGameState() const { return CurrentGameState; }

	UFUNCTION(BlueprintPure, Category = "Game State")
	EGameState GetPreviousGameState() const { return PreviousGameState; }

	UFUNCTION(BlueprintPure, Category = "Game State")
	bool IsGamePaused() const { return CurrentGameState == EGameState::Paused; }

	UFUNCTION(BlueprintPure, Category = "Game State")
	bool IsInGame() const { return CurrentGameState == EGameState::Playing; }

	// Game Control Methods
	UFUNCTION(BlueprintCallable, Category = "Game Control")
	void StartGame();

	UFUNCTION(BlueprintCallable, Category = "Game Control")
	void PauseGame();

	UFUNCTION(BlueprintCallable, Category = "Game Control")
	void ResumeGame();

	UFUNCTION(BlueprintCallable, Category = "Game Control")
	void OpenInventory();

	UFUNCTION(BlueprintCallable, Category = "Game Control")
	void CloseInventory();

	UFUNCTION(BlueprintCallable, Category = "Game Control")
	void GameOver();

	UFUNCTION(BlueprintCallable, Category = "Game Control")
	void ReturnToMenu();

	UFUNCTION(BlueprintCallable, Category = "Game Control")
	void QuitGame();

	// Save/Load System
	UFUNCTION(BlueprintCallable, Category = "Save System")
	void SaveGame();

	UFUNCTION(BlueprintCallable, Category = "Save System")
	void LoadGame();

	// Utility Methods
	UFUNCTION(BlueprintPure, Category = "Game Time")
	float GetGameTime() const { return GameTime; }

	UFUNCTION(BlueprintCallable, Category = "Game Time")
	void ResetGameTime() { GameTime = 0.0f; }

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Game Events")
	FOnGameStateChanged OnGameStateChanged;

protected:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Game Settings")
	bool bDebugMode = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Game Settings")
	float AutoSaveInterval = 300.0f; // 5 minutes

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scene Management")
	FString MainMenuLevelName = TEXT("MainMenu");

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scene Management")
	FString GameLevelName = TEXT("SpaceStation");

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scene Management")
	FString LoadingLevelName = TEXT("Loading");

private:
	UPROPERTY()
	EGameState CurrentGameState = EGameState::Menu;

	UPROPERTY()
	EGameState PreviousGameState = EGameState::Menu;

	float GameTime = 0.0f;
	FTimerHandle AutoSaveTimerHandle;

	void HandleGameInput();
	void InitializeGame();
	void StartAutoSave();
	void AutoSaveGame();
	void LoadGameScene();
	void LoadMenuScene();
};
