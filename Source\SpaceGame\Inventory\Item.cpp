#include "Item.h"
#include "Player/PlayerResourceComponent.h"
#include "Kismet/GameplayStatics.h"
#include "SpaceGame.h"

UItem::UItem()
{
	ItemName = FText::FromString(TEXT("New Item"));
	Description = FText::FromString(TEXT("A new item"));
	MaxStackSize = 1;
	Weight = 1.0f;
	Value = 1;
	bHasDurability = false;
	MaxDurability = 100.0f;
	bIsConsumable = false;
	bCanBeDropped = true;
	bCanBeSold = true;
}

bool UItem::Use(AActor* User)
{
	if (!CanUse(User))
	{
		return false;
	}

	// Handle consumable effects
	if (bIsConsumable && User)
	{
		if (UPlayerResourceComponent* ResourceComponent = User->FindComponentByClass<UPlayerResourceComponent>())
		{
			if (HealthRestore > 0.0f)
			{
				ResourceComponent->Heal(HealthRestore);
			}
			
			if (HungerRestore > 0.0f)
			{
				ResourceComponent->RestoreHunger(HungerRestore);
			}
			
			if (OxygenRestore > 0.0f)
			{
				ResourceComponent->RestoreOxygen(OxygenRestore);
			}
			
			if (EnergyRestore > 0.0f)
			{
				ResourceComponent->RestoreEnergy(EnergyRestore);
			}
		}

		// Play use sound
		if (UseSound && User)
		{
			UGameplayStatics::PlaySoundAtLocation(User, UseSound, User->GetActorLocation());
		}

		// Call Blueprint event
		if (OnUse(User))
		{
			return true;
		}

		return true; // Consumable was used successfully
	}

	// For non-consumables, call Blueprint event
	return OnUse(User);
}

bool UItem::CanUse(AActor* User) const
{
	// Basic checks
	if (!User)
	{
		return false;
	}

	// Call Blueprint event for custom logic
	return OnCanUse(User);
}

FLinearColor UItem::GetRarityColor() const
{
	switch (Rarity)
	{
	case EItemRarity::Common:
		return FLinearColor::White;
	case EItemRarity::Uncommon:
		return FLinearColor::Green;
	case EItemRarity::Rare:
		return FLinearColor::Blue;
	case EItemRarity::Epic:
		return FLinearColor(0.5f, 0.0f, 1.0f); // Purple
	case EItemRarity::Legendary:
		return FLinearColor(1.0f, 0.5f, 0.0f); // Orange
	default:
		return FLinearColor::White;
	}
}

FText UItem::GetFormattedDescription() const
{
	FString FormattedDesc = Description.ToString();

	// Add effects information for consumables
	if (bIsConsumable)
	{
		if (HealthRestore > 0.0f)
		{
			FormattedDesc += FString::Printf(TEXT("\n+%.0f Health"), HealthRestore);
		}
		if (HungerRestore > 0.0f)
		{
			FormattedDesc += FString::Printf(TEXT("\n+%.0f Hunger"), HungerRestore);
		}
		if (OxygenRestore > 0.0f)
		{
			FormattedDesc += FString::Printf(TEXT("\n+%.0f Oxygen"), OxygenRestore);
		}
		if (EnergyRestore > 0.0f)
		{
			FormattedDesc += FString::Printf(TEXT("\n+%.0f Energy"), EnergyRestore);
		}
	}

	// Add weight and value information
	FormattedDesc += FString::Printf(TEXT("\nWeight: %.1f"), Weight);
	FormattedDesc += FString::Printf(TEXT("\nValue: %d"), Value);

	return FText::FromString(FormattedDesc);
}

float UItem::GetTotalWeight(int32 Quantity) const
{
	return Weight * Quantity;
}

FPrimaryAssetId UItem::GetPrimaryAssetId() const
{
	return FPrimaryAssetId(TEXT("Item"), GetFName());
}
