#pragma once

#include "CoreMinimal.h"
#include "Engine/DataAsset.h"
#include "Engine/Texture2D.h"
#include "Item.generated.h"

UENUM(BlueprintType)
enum class EItemType : uint8
{
	Consumable		UMETA(DisplayName = "Consumable"),
	Equipment		UMETA(DisplayName = "Equipment"),
	Weapon			UMETA(DisplayName = "Weapon"),
	Tool			UMETA(DisplayName = "Tool"),
	Resource		UMETA(DisplayName = "Resource"),
	Quest			UMETA(DisplayName = "Quest"),
	Misc			UMETA(DisplayName = "Miscellaneous")
};

UENUM(BlueprintType)
enum class EItemRarity : uint8
{
	Common			UMETA(DisplayName = "Common"),
	Uncommon		UMETA(DisplayName = "Uncommon"),
	Rare			UMETA(DisplayName = "Rare"),
	Epic			UMETA(DisplayName = "Epic"),
	Legendary		UMETA(DisplayName = "Legendary")
};

UCLASS(BlueprintType, Blueprintable)
class SPACEGAME_API UItem : public UPrimaryDataAsset
{
	GENERATED_BODY()

public:
	UItem();

	// Basic Item Properties
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Item")
	FText ItemName;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Item")
	FText Description;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Item")
	UTexture2D* Icon;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Item")
	UStaticMesh* WorldMesh;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Item")
	EItemType ItemType = EItemType::Misc;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Item")
	EItemRarity Rarity = EItemRarity::Common;

	// Stack and Weight Properties
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Item")
	int32 MaxStackSize = 1;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Item")
	float Weight = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Item")
	int32 Value = 1;

	// Durability Properties
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Item")
	bool bHasDurability = false;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Item", meta = (EditCondition = "bHasDurability"))
	float MaxDurability = 100.0f;

	// Usage Properties
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Item")
	bool bIsConsumable = false;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Item")
	bool bCanBeDropped = true;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Item")
	bool bCanBeSold = true;

	// Effects (for consumables)
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Effects", meta = (EditCondition = "bIsConsumable"))
	float HealthRestore = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Effects", meta = (EditCondition = "bIsConsumable"))
	float HungerRestore = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Effects", meta = (EditCondition = "bIsConsumable"))
	float OxygenRestore = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Effects", meta = (EditCondition = "bIsConsumable"))
	float EnergyRestore = 0.0f;

	// Audio
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Audio")
	USoundBase* UseSound;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Audio")
	USoundBase* PickupSound;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Audio")
	USoundBase* DropSound;

public:
	// Item Usage
	UFUNCTION(BlueprintCallable, Category = "Item")
	virtual bool Use(AActor* User);

	UFUNCTION(BlueprintPure, Category = "Item")
	virtual bool CanUse(AActor* User) const;

	// Utility Functions
	UFUNCTION(BlueprintPure, Category = "Item")
	FLinearColor GetRarityColor() const;

	UFUNCTION(BlueprintPure, Category = "Item")
	FText GetFormattedDescription() const;

	UFUNCTION(BlueprintPure, Category = "Item")
	float GetTotalWeight(int32 Quantity) const;

	// Data Asset Interface
	virtual FPrimaryAssetId GetPrimaryAssetId() const override;

protected:
	// Override in Blueprint or derived classes for custom use behavior
	UFUNCTION(BlueprintImplementableEvent, Category = "Item")
	bool OnUse(AActor* User);

	UFUNCTION(BlueprintImplementableEvent, Category = "Item")
	bool OnCanUse(AActor* User) const;
};
