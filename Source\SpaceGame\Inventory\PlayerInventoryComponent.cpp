#include "PlayerInventoryComponent.h"
#include "Components/AudioComponent.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/World.h"
#include "SpaceGame.h"

UPlayerInventoryComponent::UPlayerInventoryComponent()
{
	PrimaryComponentTick.bCanEverTick = false;
}

void UPlayerInventoryComponent::BeginPlay()
{
	Super::BeginPlay();
	
	InitializeInventory();
}

void UPlayerInventoryComponent::InitializeInventory()
{
	// Create audio component
	if (AActor* Owner = GetOwner())
	{
		AudioComponent = Owner->FindComponentByClass<UAudioComponent>();
		if (!AudioComponent)
		{
			AudioComponent = NewObject<UAudioComponent>(Owner);
			AudioComponent->SetupAttachment(Owner->GetRootComponent());
			AudioComponent->RegisterComponent();
		}
	}

	// Initialize inventory and quick slots
	Inventory.Empty();
	QuickSlots.SetNum(QuickSlotCount);
}

bool UPlayerInventoryComponent::AddItem(UItem* Item, int32 Quantity)
{
	if (!Item || Quantity <= 0)
	{
		return false;
	}

	// Check weight limit
	if (!bUnlimitedWeight && GetCurrentWeight() + Item->GetTotalWeight(Quantity) > MaxWeight)
	{
		PlaySound(InventoryFullSound);
		return false;
	}

	int32 RemainingQuantity = Quantity;

	// Try to stack with existing items
	for (FItemStack& Stack : Inventory)
	{
		if (Stack.CanStackWith(Item))
		{
			RemainingQuantity = Stack.AddToStack(RemainingQuantity);
			if (RemainingQuantity <= 0) break;
		}
	}

	// Create new stacks for remaining quantity
	while (RemainingQuantity > 0 && Inventory.Num() < InventorySize)
	{
		int32 StackSize = FMath::Min(RemainingQuantity, Item->MaxStackSize);
		Inventory.Add(FItemStack(Item, StackSize));
		RemainingQuantity -= StackSize;
	}

	// Check if we couldn't add all items
	if (RemainingQuantity > 0)
	{
		PlaySound(InventoryFullSound);
		return false;
	}

	PlaySound(ItemPickupSound);
	OnInventoryChanged.Broadcast();
	return true;
}

bool UPlayerInventoryComponent::RemoveItem(UItem* Item, int32 Quantity)
{
	if (!Item || Quantity <= 0)
	{
		return false;
	}

	int32 RemainingToRemove = Quantity;

	for (int32 i = Inventory.Num() - 1; i >= 0; i--)
	{
		FItemStack& Stack = Inventory[i];
		if (Stack.Item == Item)
		{
			int32 RemoveFromStack = FMath::Min(RemainingToRemove, Stack.Quantity);
			Stack.Quantity -= RemoveFromStack;
			RemainingToRemove -= RemoveFromStack;

			if (Stack.IsEmpty())
			{
				Inventory.RemoveAt(i);
			}

			if (RemainingToRemove <= 0) break;
		}
	}

	if (RemainingToRemove < Quantity)
	{
		OnInventoryChanged.Broadcast();
		return true;
	}

	return false;
}

int32 UPlayerInventoryComponent::GetItemCount(UItem* Item) const
{
	int32 Count = 0;
	for (const FItemStack& Stack : Inventory)
	{
		if (Stack.Item == Item)
		{
			Count += Stack.Quantity;
		}
	}
	return Count;
}

bool UPlayerInventoryComponent::HasItem(UItem* Item, int32 Quantity) const
{
	return GetItemCount(Item) >= Quantity;
}

bool UPlayerInventoryComponent::UseItem(UItem* Item, int32 Quantity)
{
	if (!HasItem(Item, Quantity))
	{
		return false;
	}

	bool bSuccess = false;
	for (int32 i = 0; i < Quantity; i++)
	{
		if (Item->Use(GetOwner()))
		{
			RemoveItem(Item, 1);
			bSuccess = true;
		}
		else
		{
			break;
		}
	}

	return bSuccess;
}

void UPlayerInventoryComponent::DropItem(UItem* Item, int32 Quantity)
{
	if (!HasItem(Item, Quantity))
	{
		return;
	}

	RemoveItem(Item, Quantity);

	// TODO: Create world item actor
	// This would spawn a pickup in the world that the player can collect
	
	PlaySound(ItemDropSound);
}

void UPlayerInventoryComponent::SetQuickSlot(int32 SlotIndex, const FItemStack& ItemStack)
{
	if (SlotIndex >= 0 && SlotIndex < QuickSlotCount)
	{
		QuickSlots[SlotIndex] = ItemStack;
		OnQuickSlotChanged.Broadcast(SlotIndex);
	}
}

FItemStack UPlayerInventoryComponent::GetQuickSlot(int32 SlotIndex) const
{
	if (SlotIndex >= 0 && SlotIndex < QuickSlotCount)
	{
		return QuickSlots[SlotIndex];
	}
	return FItemStack();
}

void UPlayerInventoryComponent::UseQuickSlot(int32 SlotIndex)
{
	FItemStack QuickSlot = GetQuickSlot(SlotIndex);
	if (QuickSlot.Item && !QuickSlot.IsEmpty())
	{
		UseItem(QuickSlot.Item, 1);
	}
}

void UPlayerInventoryComponent::ClearInventory()
{
	Inventory.Empty();
	for (int32 i = 0; i < QuickSlotCount; i++)
	{
		QuickSlots[i] = FItemStack();
	}
	OnInventoryChanged.Broadcast();
}

float UPlayerInventoryComponent::GetCurrentWeight() const
{
	float TotalWeight = 0.0f;
	for (const FItemStack& Stack : Inventory)
	{
		TotalWeight += Stack.GetTotalWeight();
	}
	return TotalWeight;
}

bool UPlayerInventoryComponent::IsInventoryFull() const
{
	return Inventory.Num() >= InventorySize;
}

bool UPlayerInventoryComponent::IsOverweight() const
{
	return !bUnlimitedWeight && GetCurrentWeight() > MaxWeight;
}

FInventorySaveData UPlayerInventoryComponent::GetSaveData() const
{
	FInventorySaveData SaveData;
	SaveData.Items = Inventory;
	SaveData.QuickSlots = QuickSlots;
	return SaveData;
}

void UPlayerInventoryComponent::LoadSaveData(const FInventorySaveData& SaveData)
{
	ClearInventory();
	Inventory = SaveData.Items;
	QuickSlots = SaveData.QuickSlots;
	OnInventoryChanged.Broadcast();
}

void UPlayerInventoryComponent::PlaySound(USoundBase* Sound)
{
	if (Sound && AudioComponent)
	{
		AudioComponent->SetSound(Sound);
		AudioComponent->Play();
	}
}
