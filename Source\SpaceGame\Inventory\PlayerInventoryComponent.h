#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Item.h"
#include "PlayerInventoryComponent.generated.h"

USTRUCT(BlueprintType)
struct SPACEGAME_API FItemStack
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item Stack")
	UItem* Item = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item Stack")
	int32 Quantity = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item Stack")
	float Durability = 100.0f;

	FItemStack()
	{
		Item = nullptr;
		Quantity = 0;
		Durability = 100.0f;
	}

	FItemStack(UItem* InItem, int32 InQuantity, float InDurability = 100.0f)
	{
		Item = InItem;
		Quantity = InQuantity;
		Durability = InDurability;
	}

	bool IsEmpty() const
	{
		return Item == nullptr || Quantity <= 0;
	}

	bool CanStackWith(UItem* OtherItem) const
	{
		return Item && OtherItem && Item == OtherItem && !Item->bHasDurability;
	}

	int32 AddToStack(int32 AmountToAdd)
	{
		if (!Item) return AmountToAdd;

		int32 MaxCanAdd = Item->MaxStackSize - Quantity;
		int32 ActuallyAdded = FMath::Min(AmountToAdd, MaxCanAdd);
		Quantity += ActuallyAdded;
		return AmountToAdd - ActuallyAdded;
	}

	float GetTotalWeight() const
	{
		return Item ? Item->GetTotalWeight(Quantity) : 0.0f;
	}
};

USTRUCT(BlueprintType)
struct SPACEGAME_API FInventorySaveData
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	TArray<FItemStack> Items;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	TArray<FItemStack> QuickSlots;
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnInventoryChanged);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnQuickSlotChanged, int32, SlotIndex);

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class SPACEGAME_API UPlayerInventoryComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	UPlayerInventoryComponent();

protected:
	virtual void BeginPlay() override;

	// Inventory Settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Inventory")
	int32 InventorySize = 30;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Inventory")
	float MaxWeight = 100.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Inventory")
	bool bUnlimitedWeight = false;

	// Quick Slots Settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Quick Slots")
	int32 QuickSlotCount = 6;

	// Audio
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
	USoundBase* ItemPickupSound;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
	USoundBase* ItemDropSound;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
	USoundBase* InventoryFullSound;

public:
	// Item Management
	UFUNCTION(BlueprintCallable, Category = "Inventory")
	bool AddItem(UItem* Item, int32 Quantity = 1);

	UFUNCTION(BlueprintCallable, Category = "Inventory")
	bool RemoveItem(UItem* Item, int32 Quantity = 1);

	UFUNCTION(BlueprintCallable, Category = "Inventory")
	int32 GetItemCount(UItem* Item) const;

	UFUNCTION(BlueprintCallable, Category = "Inventory")
	bool HasItem(UItem* Item, int32 Quantity = 1) const;

	UFUNCTION(BlueprintCallable, Category = "Inventory")
	bool UseItem(UItem* Item, int32 Quantity = 1);

	UFUNCTION(BlueprintCallable, Category = "Inventory")
	void DropItem(UItem* Item, int32 Quantity = 1);

	// Quick Slots
	UFUNCTION(BlueprintCallable, Category = "Quick Slots")
	void SetQuickSlot(int32 SlotIndex, const FItemStack& ItemStack);

	UFUNCTION(BlueprintCallable, Category = "Quick Slots")
	FItemStack GetQuickSlot(int32 SlotIndex) const;

	UFUNCTION(BlueprintCallable, Category = "Quick Slots")
	void UseQuickSlot(int32 SlotIndex);

	// Inventory Management
	UFUNCTION(BlueprintCallable, Category = "Inventory")
	void ClearInventory();

	// Getters
	UFUNCTION(BlueprintPure, Category = "Inventory")
	int32 GetInventorySize() const { return InventorySize; }

	UFUNCTION(BlueprintPure, Category = "Inventory")
	float GetCurrentWeight() const;

	UFUNCTION(BlueprintPure, Category = "Inventory")
	float GetMaxWeight() const { return MaxWeight; }

	UFUNCTION(BlueprintPure, Category = "Inventory")
	bool IsInventoryFull() const;

	UFUNCTION(BlueprintPure, Category = "Inventory")
	bool IsOverweight() const;

	UFUNCTION(BlueprintPure, Category = "Inventory")
	TArray<FItemStack> GetItems() const { return Inventory; }

	UFUNCTION(BlueprintPure, Category = "Quick Slots")
	TArray<FItemStack> GetQuickSlots() const { return QuickSlots; }

	// Save/Load
	UFUNCTION(BlueprintCallable, Category = "Save System")
	FInventorySaveData GetSaveData() const;

	UFUNCTION(BlueprintCallable, Category = "Save System")
	void LoadSaveData(const FInventorySaveData& SaveData);

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnInventoryChanged OnInventoryChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnQuickSlotChanged OnQuickSlotChanged;

private:
	UPROPERTY()
	TArray<FItemStack> Inventory;

	UPROPERTY()
	TArray<FItemStack> QuickSlots;

	UPROPERTY()
	UAudioComponent* AudioComponent;

	void InitializeInventory();
	void PlaySound(USoundBase* Sound);
};
