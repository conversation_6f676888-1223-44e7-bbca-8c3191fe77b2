#include "PlayerResourceComponent.h"
#include "Components/AudioComponent.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Core/SpaceEventManager.h"
#include "Core/SpaceGameModeBase.h"
#include "SpacePlayerPawn.h"
#include "SpaceGame.h"

UPlayerResourceComponent::UPlayerResourceComponent()
{
	PrimaryComponentTick.bCanEverTick = true;
}

void UPlayerResourceComponent::BeginPlay()
{
	Super::BeginPlay();
	
	InitializeResources();
}

void UPlayerResourceComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	UpdateResources(DeltaTime);
	HandleEffects();
}

void UPlayerResourceComponent::InitializeResources()
{
	// Create audio component for effects
	if (AActor* Owner = GetOwner())
	{
		EffectAudioComponent = Owner->FindComponentByClass<UAudioComponent>();
		if (!EffectAudioComponent)
		{
			EffectAudioComponent = NewObject<UAudioComponent>(Owner);
			EffectAudioComponent->SetupAttachment(Owner->GetRootComponent());
			EffectAudioComponent->RegisterComponent();
		}
	}

	// Initialize resources to max
	CurrentHealth = MaxHealth;
	CurrentHunger = MaxHunger;
	CurrentOxygen = MaxOxygen;
	CurrentEnergy = MaxEnergy;

	LastDamageTime = GetWorld()->GetTimeSeconds();
}

void UPlayerResourceComponent::UpdateResources(float DeltaTime)
{
	UpdateHunger(DeltaTime);
	UpdateOxygen(DeltaTime);
	UpdateEnergy(DeltaTime);
	UpdateHealthRegeneration();
}

void UPlayerResourceComponent::UpdateHunger(float DeltaTime)
{
	float PreviousHunger = CurrentHunger;
	
	// Decrease hunger over time
	CurrentHunger = FMath::Max(0.0f, CurrentHunger - HungerDecayRate * DeltaTime);

	// Damage from hunger
	if (CurrentHunger <= HungerDamageThreshold)
	{
		TakeDamage(HungerDamageRate * DeltaTime, false);
	}

	// Trigger event if changed
	if (FMath::Abs(CurrentHunger - PreviousHunger) > 0.01f)
	{
		OnHungerChanged.Broadcast(CurrentHunger, MaxHunger, PreviousHunger);
		
		if (USpaceEventManager* EventManager = USpaceEventManager::GetInstance(this))
		{
			EventManager->TriggerPlayerHungerChanged(CurrentHunger, MaxHunger, PreviousHunger);
		}
	}
}

void UPlayerResourceComponent::UpdateOxygen(float DeltaTime)
{
	float PreviousOxygen = CurrentOxygen;

	if (!bIsInOxygenZone)
	{
		// Decrease oxygen when not in oxygen zone
		CurrentOxygen = FMath::Max(0.0f, CurrentOxygen - OxygenDecayRate * DeltaTime);

		// Damage from lack of oxygen
		if (CurrentOxygen <= 0.0f)
		{
			TakeDamage(OxygenDamageRate * DeltaTime, false);
		}
	}
	else
	{
		// Regenerate oxygen when in oxygen zone
		CurrentOxygen = FMath::Min(MaxOxygen, CurrentOxygen + OxygenDecayRate * 2.0f * DeltaTime);
	}

	// Trigger event if changed
	if (FMath::Abs(CurrentOxygen - PreviousOxygen) > 0.01f)
	{
		OnOxygenChanged.Broadcast(CurrentOxygen, MaxOxygen, PreviousOxygen);
		
		if (USpaceEventManager* EventManager = USpaceEventManager::GetInstance(this))
		{
			EventManager->TriggerPlayerOxygenChanged(CurrentOxygen, MaxOxygen, PreviousOxygen);
		}
	}
}

void UPlayerResourceComponent::UpdateEnergy(float DeltaTime)
{
	float PreviousEnergy = CurrentEnergy;
	
	bool bIsMoving = false;
	bool bIsSprinting = false;

	// Check if player is moving/sprinting
	if (ASpacePlayerPawn* PlayerPawn = Cast<ASpacePlayerPawn>(GetOwner()))
	{
		bIsMoving = PlayerPawn->IsMoving();
		bIsSprinting = PlayerPawn->IsSprinting();
	}

	if (bIsSprinting && bIsMoving)
	{
		// Drain energy when sprinting
		CurrentEnergy = FMath::Max(0.0f, CurrentEnergy - SprintEnergyDrain * DeltaTime);
	}
	else if (!bIsMoving || CurrentEnergy < MaxEnergy)
	{
		// Regenerate energy when not moving or not at max
		CurrentEnergy = FMath::Min(MaxEnergy, CurrentEnergy + EnergyRegenRate * DeltaTime);
	}

	// Trigger event if changed
	if (FMath::Abs(CurrentEnergy - PreviousEnergy) > 0.01f)
	{
		OnEnergyChanged.Broadcast(CurrentEnergy, MaxEnergy, PreviousEnergy);
	}
}

void UPlayerResourceComponent::UpdateHealthRegeneration()
{
	// Start health regeneration if enough time has passed since last damage
	if (!bIsRegeneratingHealth && 
		GetWorld()->GetTimeSeconds() - LastDamageTime >= HealthRegenDelay && 
		CurrentHealth < MaxHealth)
	{
		StartHealthRegeneration();
	}
}

void UPlayerResourceComponent::StartHealthRegeneration()
{
	bIsRegeneratingHealth = true;
	
	GetWorld()->GetTimerManager().SetTimer(HealthRegenTimerHandle, [this]()
	{
		if (CurrentHealth < MaxHealth && GetWorld()->GetTimeSeconds() - LastDamageTime >= HealthRegenDelay)
		{
			float PreviousHealth = CurrentHealth;
			CurrentHealth = FMath::Min(MaxHealth, CurrentHealth + HealthRegenRate * GetWorld()->GetDeltaSeconds());
			TriggerHealthChangedEvent(PreviousHealth);
		}
		else
		{
			bIsRegeneratingHealth = false;
			GetWorld()->GetTimerManager().ClearTimer(HealthRegenTimerHandle);
		}
	}, 0.1f, true);
}

void UPlayerResourceComponent::HandleEffects()
{
	if (!EffectAudioComponent) return;

	// Handle low health effects
	if (IsHealthCritical())
	{
		// Play heartbeat sound
		if (HeartbeatSound && !EffectAudioComponent->IsPlaying())
		{
			EffectAudioComponent->SetSound(HeartbeatSound);
			EffectAudioComponent->SetVolumeMultiplier(1.0f);
			EffectAudioComponent->Play();
		}
	}
	else if (IsHealthLow())
	{
		// Slower heartbeat for low health
		if (HeartbeatSound && (!EffectAudioComponent->IsPlaying() || EffectAudioComponent->Sound != HeartbeatSound))
		{
			EffectAudioComponent->SetSound(HeartbeatSound);
			EffectAudioComponent->SetPitchMultiplier(0.8f);
			EffectAudioComponent->Play();
		}
	}
	else
	{
		// Stop health effects
		if (EffectAudioComponent->IsPlaying() && EffectAudioComponent->Sound == HeartbeatSound)
		{
			EffectAudioComponent->Stop();
			EffectAudioComponent->SetPitchMultiplier(1.0f);
		}
	}

	// Handle oxygen effects
	if (IsOutOfOxygen())
	{
		if (BreathingSound && (!EffectAudioComponent->IsPlaying() || EffectAudioComponent->Sound != BreathingSound))
		{
			EffectAudioComponent->SetSound(BreathingSound);
			EffectAudioComponent->Play();
		}
	}
}

void UPlayerResourceComponent::TakeDamage(float Damage, bool bResetRegenTimer)
{
	float PreviousHealth = CurrentHealth;
	CurrentHealth = FMath::Max(0.0f, CurrentHealth - Damage);

	if (bResetRegenTimer)
	{
		LastDamageTime = GetWorld()->GetTimeSeconds();

		if (bIsRegeneratingHealth)
		{
			GetWorld()->GetTimerManager().ClearTimer(HealthRegenTimerHandle);
			bIsRegeneratingHealth = false;
		}
	}

	TriggerHealthChangedEvent(PreviousHealth);

	// Check for death
	if (CurrentHealth <= 0.0f)
	{
		Die();
	}
}

void UPlayerResourceComponent::Heal(float Amount)
{
	float PreviousHealth = CurrentHealth;
	CurrentHealth = FMath::Min(MaxHealth, CurrentHealth + Amount);
	TriggerHealthChangedEvent(PreviousHealth);
}

void UPlayerResourceComponent::RestoreHunger(float Amount)
{
	float PreviousHunger = CurrentHunger;
	CurrentHunger = FMath::Min(MaxHunger, CurrentHunger + Amount);
	
	OnHungerChanged.Broadcast(CurrentHunger, MaxHunger, PreviousHunger);
	
	if (USpaceEventManager* EventManager = USpaceEventManager::GetInstance(this))
	{
		EventManager->TriggerPlayerHungerChanged(CurrentHunger, MaxHunger, PreviousHunger);
	}
}

void UPlayerResourceComponent::RestoreOxygen(float Amount)
{
	float PreviousOxygen = CurrentOxygen;
	CurrentOxygen = FMath::Min(MaxOxygen, CurrentOxygen + Amount);
	
	OnOxygenChanged.Broadcast(CurrentOxygen, MaxOxygen, PreviousOxygen);
	
	if (USpaceEventManager* EventManager = USpaceEventManager::GetInstance(this))
	{
		EventManager->TriggerPlayerOxygenChanged(CurrentOxygen, MaxOxygen, PreviousOxygen);
	}
}

void UPlayerResourceComponent::RestoreEnergy(float Amount)
{
	float PreviousEnergy = CurrentEnergy;
	CurrentEnergy = FMath::Min(MaxEnergy, CurrentEnergy + Amount);
	OnEnergyChanged.Broadcast(CurrentEnergy, MaxEnergy, PreviousEnergy);
}

bool UPlayerResourceComponent::CanSprint() const
{
	return CurrentEnergy > 10.0f;
}

bool UPlayerResourceComponent::CanJump() const
{
	return CurrentEnergy >= JumpEnergyDrain;
}

void UPlayerResourceComponent::ConsumeEnergyForJump()
{
	float PreviousEnergy = CurrentEnergy;
	CurrentEnergy = FMath::Max(0.0f, CurrentEnergy - JumpEnergyDrain);
	OnEnergyChanged.Broadcast(CurrentEnergy, MaxEnergy, PreviousEnergy);
}

void UPlayerResourceComponent::SetOxygenZone(bool bInOxygenZone)
{
	bIsInOxygenZone = bInOxygenZone;
}

void UPlayerResourceComponent::TriggerHealthChangedEvent(float PreviousHealth)
{
	if (PreviousHealth < 0.0f) PreviousHealth = CurrentHealth;
	
	OnHealthChanged.Broadcast(CurrentHealth, MaxHealth, PreviousHealth);
	
	if (USpaceEventManager* EventManager = USpaceEventManager::GetInstance(this))
	{
		EventManager->TriggerPlayerHealthChanged(CurrentHealth, MaxHealth, PreviousHealth);
	}
}

void UPlayerResourceComponent::Die()
{
	if (ASpaceGameModeBase* GameMode = Cast<ASpaceGameModeBase>(GetWorld()->GetAuthGameMode()))
	{
		GameMode->GameOver();
	}

	UE_LOG(LogSpaceGame, Log, TEXT("Player died!"));
}
