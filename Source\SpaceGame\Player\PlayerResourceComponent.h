#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Engine/Engine.h"
#include "PlayerResourceComponent.generated.h"

class USpaceEventManager;
class UAudioComponent;

DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnHealthChanged, float, CurrentHealth, float, MaxHealth, float, PreviousHealth);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnHungerChanged, float, CurrentHunger, float, MaxHunger, float, PreviousHunger);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnOxygenChanged, float, CurrentOxygen, float, MaxOxygen, float, PreviousOxygen);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnEnergyChanged, float, CurrentEnergy, float, MaxEnergy, float, PreviousEnergy);

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class SPACEGAME_API UPlayerResourceComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	UPlayerResourceComponent();

protected:
	virtual void BeginPlay() override;
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	// Health Settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Health")
	float MaxHealth = 100.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Health")
	float HealthRegenRate = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Health")
	float HealthRegenDelay = 5.0f;

	// Hunger Settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hunger")
	float MaxHunger = 100.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hunger")
	float HungerDecayRate = 0.5f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hunger")
	float HungerDamageThreshold = 20.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Hunger")
	float HungerDamageRate = 2.0f;

	// Oxygen Settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Oxygen")
	float MaxOxygen = 100.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Oxygen")
	float OxygenDecayRate = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Oxygen")
	float OxygenDamageRate = 5.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Oxygen")
	bool bIsInOxygenZone = true;

	// Energy Settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Energy")
	float MaxEnergy = 100.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Energy")
	float EnergyRegenRate = 2.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Energy")
	float SprintEnergyDrain = 10.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Energy")
	float JumpEnergyDrain = 15.0f;

	// Effects Settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
	float LowHealthThreshold = 25.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
	float CriticalHealthThreshold = 10.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
	USoundBase* HeartbeatSound;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
	USoundBase* BreathingSound;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
	USoundBase* AlarmSound;

public:
	// Resource Management
	UFUNCTION(BlueprintCallable, Category = "Health")
	void TakeDamage(float Damage, bool bResetRegenTimer = true);

	UFUNCTION(BlueprintCallable, Category = "Health")
	void Heal(float Amount);

	UFUNCTION(BlueprintCallable, Category = "Hunger")
	void RestoreHunger(float Amount);

	UFUNCTION(BlueprintCallable, Category = "Oxygen")
	void RestoreOxygen(float Amount);

	UFUNCTION(BlueprintCallable, Category = "Energy")
	void RestoreEnergy(float Amount);

	UFUNCTION(BlueprintCallable, Category = "Energy")
	bool CanSprint() const;

	UFUNCTION(BlueprintCallable, Category = "Energy")
	bool CanJump() const;

	UFUNCTION(BlueprintCallable, Category = "Energy")
	void ConsumeEnergyForJump();

	UFUNCTION(BlueprintCallable, Category = "Oxygen")
	void SetOxygenZone(bool bInOxygenZone);

	// Getters
	UFUNCTION(BlueprintPure, Category = "Health")
	float GetHealthPercentage() const { return CurrentHealth / MaxHealth; }

	UFUNCTION(BlueprintPure, Category = "Hunger")
	float GetHungerPercentage() const { return CurrentHunger / MaxHunger; }

	UFUNCTION(BlueprintPure, Category = "Oxygen")
	float GetOxygenPercentage() const { return CurrentOxygen / MaxOxygen; }

	UFUNCTION(BlueprintPure, Category = "Energy")
	float GetEnergyPercentage() const { return CurrentEnergy / MaxEnergy; }

	UFUNCTION(BlueprintPure, Category = "Health")
	float GetCurrentHealth() const { return CurrentHealth; }

	UFUNCTION(BlueprintPure, Category = "Hunger")
	float GetCurrentHunger() const { return CurrentHunger; }

	UFUNCTION(BlueprintPure, Category = "Oxygen")
	float GetCurrentOxygen() const { return CurrentOxygen; }

	UFUNCTION(BlueprintPure, Category = "Energy")
	float GetCurrentEnergy() const { return CurrentEnergy; }

	// Status Checks
	UFUNCTION(BlueprintPure, Category = "Health")
	bool IsHealthCritical() const { return CurrentHealth <= CriticalHealthThreshold; }

	UFUNCTION(BlueprintPure, Category = "Health")
	bool IsHealthLow() const { return CurrentHealth <= LowHealthThreshold; }

	UFUNCTION(BlueprintPure, Category = "Hunger")
	bool IsHungry() const { return CurrentHunger <= HungerDamageThreshold; }

	UFUNCTION(BlueprintPure, Category = "Oxygen")
	bool IsOutOfOxygen() const { return CurrentOxygen <= 0.0f; }

	UFUNCTION(BlueprintPure, Category = "Energy")
	bool IsExhausted() const { return CurrentEnergy <= 10.0f; }

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnHealthChanged OnHealthChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnHungerChanged OnHungerChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnOxygenChanged OnOxygenChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnEnergyChanged OnEnergyChanged;

private:
	// Current resource values
	float CurrentHealth = 100.0f;
	float CurrentHunger = 100.0f;
	float CurrentOxygen = 100.0f;
	float CurrentEnergy = 100.0f;

	// Internal state
	float LastDamageTime = 0.0f;
	bool bIsRegeneratingHealth = false;
	FTimerHandle HealthRegenTimerHandle;

	UPROPERTY()
	UAudioComponent* EffectAudioComponent;

	// Internal methods
	void InitializeResources();
	void UpdateResources(float DeltaTime);
	void UpdateHunger(float DeltaTime);
	void UpdateOxygen(float DeltaTime);
	void UpdateEnergy(float DeltaTime);
	void UpdateHealthRegeneration();
	void StartHealthRegeneration();
	void HandleEffects();
	void TriggerHealthChangedEvent(float PreviousHealth = -1.0f);
	void Die();
};
