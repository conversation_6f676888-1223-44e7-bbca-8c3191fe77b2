#include "SpacePlayerController.h"
#include "EnhancedInputComponent.h"
#include "EnhancedInputSubsystems.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "DrawDebugHelpers.h"
#include "Core/InteractableInterface.h"
#include "Core/SpaceGameModeBase.h"
#include "SpacePlayerPawn.h"
#include "SpaceGame.h"

ASpacePlayerController::ASpacePlayerController()
{
	PrimaryActorTick.bCanEverTick = true;
}

void ASpacePlayerController::BeginPlay()
{
	Super::BeginPlay();

	// Add Input Mapping Context
	if (UEnhancedInputLocalPlayerSubsystem* Subsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(GetLocalPlayer()))
	{
		if (DefaultMappingContext)
		{
			Subsystem->AddMappingContext(DefaultMappingContext, 0);
		}
	}
}

void ASpacePlayerController::SetupInputComponent()
{
	Super::SetupInputComponent();

	// Set up action bindings
	if (UEnhancedInputComponent* EnhancedInputComponent = CastChecked<UEnhancedInputComponent>(InputComponent))
	{
		// Moving
		if (MoveAction)
		{
			EnhancedInputComponent->BindAction(MoveAction, ETriggerEvent::Triggered, this, &ASpacePlayerController::Move);
		}

		// Looking
		if (LookAction)
		{
			EnhancedInputComponent->BindAction(LookAction, ETriggerEvent::Triggered, this, &ASpacePlayerController::Look);
		}

		// Jumping
		if (JumpAction)
		{
			EnhancedInputComponent->BindAction(JumpAction, ETriggerEvent::Triggered, this, &ASpacePlayerController::Jump);
		}

		// Interacting
		if (InteractAction)
		{
			EnhancedInputComponent->BindAction(InteractAction, ETriggerEvent::Triggered, this, &ASpacePlayerController::Interact);
		}

		// Inventory
		if (InventoryAction)
		{
			EnhancedInputComponent->BindAction(InventoryAction, ETriggerEvent::Triggered, this, &ASpacePlayerController::ToggleInventory);
		}

		// Pause
		if (PauseAction)
		{
			EnhancedInputComponent->BindAction(PauseAction, ETriggerEvent::Triggered, this, &ASpacePlayerController::TogglePause);
		}

		// Sprint
		if (SprintAction)
		{
			EnhancedInputComponent->BindAction(SprintAction, ETriggerEvent::Triggered, this, &ASpacePlayerController::Sprint);
		}

		// Crouch
		if (CrouchAction)
		{
			EnhancedInputComponent->BindAction(CrouchAction, ETriggerEvent::Triggered, this, &ASpacePlayerController::Crouch);
		}

		// Switch Camera
		if (SwitchCameraAction)
		{
			EnhancedInputComponent->BindAction(SwitchCameraAction, ETriggerEvent::Triggered, this, &ASpacePlayerController::SwitchCamera);
		}

		// Quick Slots
		if (QuickSlot1Action)
			EnhancedInputComponent->BindAction(QuickSlot1Action, ETriggerEvent::Triggered, this, &ASpacePlayerController::UseQuickSlot1);
		if (QuickSlot2Action)
			EnhancedInputComponent->BindAction(QuickSlot2Action, ETriggerEvent::Triggered, this, &ASpacePlayerController::UseQuickSlot2);
		if (QuickSlot3Action)
			EnhancedInputComponent->BindAction(QuickSlot3Action, ETriggerEvent::Triggered, this, &ASpacePlayerController::UseQuickSlot3);
		if (QuickSlot4Action)
			EnhancedInputComponent->BindAction(QuickSlot4Action, ETriggerEvent::Triggered, this, &ASpacePlayerController::UseQuickSlot4);
		if (QuickSlot5Action)
			EnhancedInputComponent->BindAction(QuickSlot5Action, ETriggerEvent::Triggered, this, &ASpacePlayerController::UseQuickSlot5);
		if (QuickSlot6Action)
			EnhancedInputComponent->BindAction(QuickSlot6Action, ETriggerEvent::Triggered, this, &ASpacePlayerController::UseQuickSlot6);
	}
}

void ASpacePlayerController::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	// Only update interaction when in game
	if (ASpaceGameModeBase* GameMode = Cast<ASpaceGameModeBase>(GetWorld()->GetAuthGameMode()))
	{
		if (GameMode->IsInGame())
		{
			UpdateInteraction();
		}
	}
}

void ASpacePlayerController::Move(const FInputActionValue& Value)
{
	if (ASpacePlayerPawn* PlayerPawn = Cast<ASpacePlayerPawn>(GetPawn()))
	{
		PlayerPawn->Move(Value);
	}
}

void ASpacePlayerController::Look(const FInputActionValue& Value)
{
	if (ASpacePlayerPawn* PlayerPawn = Cast<ASpacePlayerPawn>(GetPawn()))
	{
		PlayerPawn->Look(Value);
	}
}

void ASpacePlayerController::Jump(const FInputActionValue& Value)
{
	if (ASpacePlayerPawn* PlayerPawn = Cast<ASpacePlayerPawn>(GetPawn()))
	{
		PlayerPawn->Jump();
	}
}

void ASpacePlayerController::Interact(const FInputActionValue& Value)
{
	if (!bIsInteracting && CurrentInteractable.GetInterface())
	{
		StartInteraction();
	}
}

void ASpacePlayerController::ToggleInventory(const FInputActionValue& Value)
{
	if (ASpaceGameModeBase* GameMode = Cast<ASpaceGameModeBase>(GetWorld()->GetAuthGameMode()))
	{
		if (GameMode->GetCurrentGameState() == EGameState::Playing)
		{
			GameMode->OpenInventory();
		}
		else if (GameMode->GetCurrentGameState() == EGameState::Inventory)
		{
			GameMode->CloseInventory();
		}
	}
}

void ASpacePlayerController::TogglePause(const FInputActionValue& Value)
{
	if (ASpaceGameModeBase* GameMode = Cast<ASpaceGameModeBase>(GetWorld()->GetAuthGameMode()))
	{
		if (GameMode->GetCurrentGameState() == EGameState::Playing)
		{
			GameMode->PauseGame();
		}
		else if (GameMode->GetCurrentGameState() == EGameState::Paused)
		{
			GameMode->ResumeGame();
		}
		else if (GameMode->GetCurrentGameState() == EGameState::Inventory)
		{
			GameMode->CloseInventory();
		}
	}
}

void ASpacePlayerController::Sprint(const FInputActionValue& Value)
{
	if (ASpacePlayerPawn* PlayerPawn = Cast<ASpacePlayerPawn>(GetPawn()))
	{
		PlayerPawn->SetSprinting(Value.Get<bool>());
	}
}

void ASpacePlayerController::Crouch(const FInputActionValue& Value)
{
	if (ASpacePlayerPawn* PlayerPawn = Cast<ASpacePlayerPawn>(GetPawn()))
	{
		PlayerPawn->ToggleCrouch();
	}
}

void ASpacePlayerController::SwitchCamera(const FInputActionValue& Value)
{
	if (ASpacePlayerPawn* PlayerPawn = Cast<ASpacePlayerPawn>(GetPawn()))
	{
		PlayerPawn->SwitchCamera();
	}
}

void ASpacePlayerController::UseQuickSlot1(const FInputActionValue& Value)
{
	// TODO: Implement quick slot usage
	UE_LOG(LogSpaceGame, Log, TEXT("Quick Slot 1 used"));
}

void ASpacePlayerController::UseQuickSlot2(const FInputActionValue& Value)
{
	// TODO: Implement quick slot usage
	UE_LOG(LogSpaceGame, Log, TEXT("Quick Slot 2 used"));
}

void ASpacePlayerController::UseQuickSlot3(const FInputActionValue& Value)
{
	// TODO: Implement quick slot usage
	UE_LOG(LogSpaceGame, Log, TEXT("Quick Slot 3 used"));
}

void ASpacePlayerController::UseQuickSlot4(const FInputActionValue& Value)
{
	// TODO: Implement quick slot usage
	UE_LOG(LogSpaceGame, Log, TEXT("Quick Slot 4 used"));
}

void ASpacePlayerController::UseQuickSlot5(const FInputActionValue& Value)
{
	// TODO: Implement quick slot usage
	UE_LOG(LogSpaceGame, Log, TEXT("Quick Slot 5 used"));
}

void ASpacePlayerController::UseQuickSlot6(const FInputActionValue& Value)
{
	// TODO: Implement quick slot usage
	UE_LOG(LogSpaceGame, Log, TEXT("Quick Slot 6 used"));
}

void ASpacePlayerController::UpdateInteraction()
{
	if (!GetPawn()) return;

	// Perform line trace from camera
	FVector Start = PlayerCameraManager->GetCameraLocation();
	FVector End = Start + (PlayerCameraManager->GetCameraRotation().Vector() * InteractionRange);

	FHitResult HitResult;
	FCollisionQueryParams QueryParams;
	QueryParams.AddIgnoredActor(GetPawn());

	bool bHit = GetWorld()->LineTraceSingleByChannel(HitResult, Start, End, InteractionTraceChannel, QueryParams);

	TScriptInterface<IInteractableInterface> NewInteractable;

	if (bHit && HitResult.GetActor())
	{
		if (HitResult.GetActor()->Implements<UInteractableInterface>())
		{
			NewInteractable.SetObject(HitResult.GetActor());
			NewInteractable.SetInterface(Cast<IInteractableInterface>(HitResult.GetActor()));

			if (NewInteractable.GetInterface() && 
				IInteractableInterface::Execute_CanInteract(NewInteractable.GetObject(), GetPawn()))
			{
				// Valid interactable found
			}
			else
			{
				NewInteractable.SetObject(nullptr);
				NewInteractable.SetInterface(nullptr);
			}
		}
	}

	// Handle interactable changes
	if (CurrentInteractable.GetInterface() != NewInteractable.GetInterface())
	{
		// Exit previous interactable
		if (CurrentInteractable.GetInterface())
		{
			IInteractableInterface::Execute_OnInteractionExit(CurrentInteractable.GetObject(), GetPawn());
		}

		// Enter new interactable
		CurrentInteractable = NewInteractable;
		if (CurrentInteractable.GetInterface())
		{
			IInteractableInterface::Execute_OnInteractionEnter(CurrentInteractable.GetObject(), GetPawn());
		}
	}
}

void ASpacePlayerController::StartInteraction()
{
	if (!CurrentInteractable.GetInterface()) return;

	bIsInteracting = true;

	// Check if this interaction requires holding
	if (IInteractableInterface::Execute_RequiresHoldToInteract(CurrentInteractable.GetObject()))
	{
		float HoldDuration = IInteractableInterface::Execute_GetHoldDuration(CurrentInteractable.GetObject());
		GetWorldTimerManager().SetTimer(InteractionTimerHandle, this, &ASpacePlayerController::CompleteInteraction, HoldDuration, false);
	}
	else
	{
		CompleteInteraction();
	}
}

void ASpacePlayerController::CompleteInteraction()
{
	if (CurrentInteractable.GetInterface())
	{
		IInteractableInterface::Execute_Interact(CurrentInteractable.GetObject(), GetPawn());
	}

	bIsInteracting = false;
	GetWorldTimerManager().ClearTimer(InteractionTimerHandle);
}

void ASpacePlayerController::CancelInteraction()
{
	bIsInteracting = false;
	GetWorldTimerManager().ClearTimer(InteractionTimerHandle);
}
