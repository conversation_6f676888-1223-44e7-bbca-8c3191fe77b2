#pragma once

#include "CoreMinimal.h"
#include "GameFramework/PlayerController.h"
#include "InputActionValue.h"
#include "Core/SpaceGameModeBase.h"
#include "SpacePlayerController.generated.h"

class UInputMappingContext;
class UInputAction;
class IInteractableInterface;

UCLASS()
class SPACEGAME_API ASpacePlayerController : public APlayerController
{
	GENERATED_BODY()

public:
	ASpacePlayerController();

protected:
	virtual void BeginPlay() override;
	virtual void SetupInputComponent() override;
	virtual void Tick(float DeltaTime) override;

	// Input Actions
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
	UInputMappingContext* DefaultMappingContext;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
	UInputAction* MoveAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
	UInputAction* LookAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
	UInputAction* JumpAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
	UInputAction* InteractAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
	UInputAction* InventoryAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
	UInputAction* PauseAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
	UInputAction* SprintAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
	UInputAction* CrouchAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
	UInputAction* SwitchCameraAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
	UInputAction* QuickSlot1Action;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
	UInputAction* QuickSlot2Action;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
	UInputAction* QuickSlot3Action;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
	UInputAction* QuickSlot4Action;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
	UInputAction* QuickSlot5Action;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input")
	UInputAction* QuickSlot6Action;

	// Input Functions
	void Move(const FInputActionValue& Value);
	void Look(const FInputActionValue& Value);
	void Jump(const FInputActionValue& Value);
	void Interact(const FInputActionValue& Value);
	void ToggleInventory(const FInputActionValue& Value);
	void TogglePause(const FInputActionValue& Value);
	void Sprint(const FInputActionValue& Value);
	void Crouch(const FInputActionValue& Value);
	void SwitchCamera(const FInputActionValue& Value);
	void UseQuickSlot1(const FInputActionValue& Value);
	void UseQuickSlot2(const FInputActionValue& Value);
	void UseQuickSlot3(const FInputActionValue& Value);
	void UseQuickSlot4(const FInputActionValue& Value);
	void UseQuickSlot5(const FInputActionValue& Value);
	void UseQuickSlot6(const FInputActionValue& Value);

	// Interaction System
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interaction")
	float InteractionRange = 300.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interaction")
	TEnumAsByte<ECollisionChannel> InteractionTraceChannel = ECC_Visibility;

private:
	UPROPERTY()
	TScriptInterface<IInteractableInterface> CurrentInteractable;

	bool bIsInteracting = false;
	FTimerHandle InteractionTimerHandle;

	void UpdateInteraction();
	void StartInteraction();
	void CompleteInteraction();
	void CancelInteraction();

public:
	// Getters
	UFUNCTION(BlueprintPure, Category = "Interaction")
	TScriptInterface<IInteractableInterface> GetCurrentInteractable() const { return CurrentInteractable; }

	UFUNCTION(BlueprintPure, Category = "Interaction")
	bool IsInteracting() const { return bIsInteracting; }
};
