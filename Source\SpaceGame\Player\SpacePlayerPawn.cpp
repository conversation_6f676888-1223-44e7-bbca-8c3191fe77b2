#include "SpacePlayerPawn.h"
#include "Camera/CameraComponent.h"
#include "GameFramework/SpringArmComponent.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Components/SkeletalMeshComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/AudioComponent.h"
#include "Particles/ParticleSystemComponent.h"
#include "Kismet/GameplayStatics.h"
#include "Sound/SoundBase.h"
#include "Engine/Engine.h"
#include "SpaceGame.h"

ASpacePlayerPawn::ASpacePlayerPawn()
{
	PrimaryActorTick.bCanEverTick = true;

	// Initialize components
	InitializeComponents();
}

void ASpacePlayerPawn::InitializeComponents()
{
	// First Person Camera
	FirstPersonCamera = CreateDefaultSubobject<UCameraComponent>(TEXT("FirstPersonCamera"));
	FirstPersonCamera->SetupAttachment(RootComponent);
	FirstPersonCamera->SetRelativeLocation(FVector(0.0f, 0.0f, 64.0f));
	FirstPersonCamera->bUsePawnControlRotation = true;

	// Third Person Spring Arm
	ThirdPersonSpringArm = CreateDefaultSubobject<USpringArmComponent>(TEXT("ThirdPersonSpringArm"));
	ThirdPersonSpringArm->SetupAttachment(RootComponent);
	ThirdPersonSpringArm->TargetArmLength = ThirdPersonDistance;
	ThirdPersonSpringArm->bUsePawnControlRotation = true;
	ThirdPersonSpringArm->bInheritPitch = true;
	ThirdPersonSpringArm->bInheritYaw = true;
	ThirdPersonSpringArm->bInheritRoll = false;

	// Third Person Camera
	ThirdPersonCamera = CreateDefaultSubobject<UCameraComponent>(TEXT("ThirdPersonCamera"));
	ThirdPersonCamera->SetupAttachment(ThirdPersonSpringArm, USpringArmComponent::SocketName);
	ThirdPersonCamera->SetActive(false);

	// First Person Mesh (arms/hands)
	FirstPersonMesh = CreateDefaultSubobject<USkeletalMeshComponent>(TEXT("FirstPersonMesh"));
	FirstPersonMesh->SetupAttachment(FirstPersonCamera);
	FirstPersonMesh->SetOnlyOwnerSee(true);
	FirstPersonMesh->bCastDynamicShadow = false;
	FirstPersonMesh->CastShadow = false;

	// Third Person Mesh (full body)
	ThirdPersonMesh = GetMesh();
	if (ThirdPersonMesh)
	{
		ThirdPersonMesh->SetOwnerNoSee(true);
	}

	// Dust Particles
	DustParticles = CreateDefaultSubobject<UParticleSystemComponent>(TEXT("DustParticles"));
	DustParticles->SetupAttachment(RootComponent);
	DustParticles->SetAutoActivate(false);

	// Footstep Audio
	FootstepAudioComponent = CreateDefaultSubobject<UAudioComponent>(TEXT("FootstepAudio"));
	FootstepAudioComponent->SetupAttachment(RootComponent);
	FootstepAudioComponent->SetAutoActivate(false);

	// Configure Character Movement
	if (UCharacterMovementComponent* CharMovement = GetCharacterMovement())
	{
		CharMovement->MaxWalkSpeed = WalkSpeed;
		CharMovement->JumpZVelocity = JumpHeight;
		CharMovement->AirControl = AirControl;
		CharMovement->bCanWalkOffLedges = true;
		CharMovement->bCanWalkOffLedgesWhenCrouching = true;
	}
}

void ASpacePlayerPawn::BeginPlay()
{
	Super::BeginPlay();
	
	SetCameraView(bIsFirstPerson);
}

void ASpacePlayerPawn::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	UpdateMovement(DeltaTime);
	UpdateCamera(DeltaTime);
	UpdateAudio(DeltaTime);
	UpdateAnimations();
}

void ASpacePlayerPawn::Move(const FInputActionValue& Value)
{
	FVector2D MovementVector = Value.Get<FVector2D>();

	if (Controller != nullptr)
	{
		// Find out which way is forward
		const FRotator Rotation = Controller->GetControlRotation();
		const FRotator YawRotation(0, Rotation.Yaw, 0);

		// Get forward vector
		const FVector ForwardDirection = FRotationMatrix(YawRotation).GetUnitAxis(EAxis::X);
		
		// Get right vector 
		const FVector RightDirection = FRotationMatrix(YawRotation).GetUnitAxis(EAxis::Y);

		// Add movement 
		AddMovementInput(ForwardDirection, MovementVector.Y);
		AddMovementInput(RightDirection, MovementVector.X);

		// Store last move direction for other systems
		LastMoveDirection = (ForwardDirection * MovementVector.Y + RightDirection * MovementVector.X).GetSafeNormal();
	}
}

void ASpacePlayerPawn::Look(const FInputActionValue& Value)
{
	FVector2D LookAxisVector = Value.Get<FVector2D>();

	if (Controller != nullptr)
	{
		// Add yaw and pitch input to controller
		AddControllerYawInput(LookAxisVector.X * MouseSensitivity);
		AddControllerPitchInput(LookAxisVector.Y * MouseSensitivity);
	}
}

void ASpacePlayerPawn::Jump()
{
	if (CanJump())
	{
		Super::Jump();
		
		// Play jump sound
		if (JumpSound)
		{
			UGameplayStatics::PlaySoundAtLocation(this, JumpSound, GetActorLocation());
		}
	}
}

void ASpacePlayerPawn::SetSprinting(bool bSprinting)
{
	bIsSprinting = bSprinting && !bIsCrouching && IsMoving();
	
	// Update movement speed
	if (UCharacterMovementComponent* CharMovement = GetCharacterMovement())
	{
		if (bIsSprinting)
		{
			CharMovement->MaxWalkSpeed = SprintSpeed;
		}
		else if (bIsCrouching)
		{
			CharMovement->MaxWalkSpeed = CrouchSpeed;
		}
		else
		{
			CharMovement->MaxWalkSpeed = WalkSpeed;
		}
	}
}

void ASpacePlayerPawn::ToggleCrouch()
{
	if (bIsCrouching)
	{
		UnCrouch();
	}
	else
	{
		Crouch();
	}
}

void ASpacePlayerPawn::SwitchCamera()
{
	SetCameraView(!bIsFirstPerson);
}

bool ASpacePlayerPawn::IsMoving() const
{
	return GetVelocity().Size() > 10.0f;
}

void ASpacePlayerPawn::UpdateMovement(float DeltaTime)
{
	// Update sprinting state
	if (bIsSprinting && (!IsMoving() || bIsCrouching))
	{
		SetSprinting(false);
	}

	// Update dust particles
	if (DustParticles)
	{
		if (IsMoving() && GetCharacterMovement()->IsMovingOnGround())
		{
			if (!DustParticles->IsActive())
			{
				DustParticles->Activate();
			}
		}
		else
		{
			if (DustParticles->IsActive())
			{
				DustParticles->Deactivate();
			}
		}
	}
}

void ASpacePlayerPawn::UpdateCamera(float DeltaTime)
{
	// Handle camera transition if needed
	if (CurrentCameraTransition > 0.0f)
	{
		CurrentCameraTransition -= DeltaTime * CameraTransitionSpeed;
		if (CurrentCameraTransition <= 0.0f)
		{
			CurrentCameraTransition = 0.0f;
		}
	}
}

void ASpacePlayerPawn::UpdateAudio(float DeltaTime)
{
	// Handle footstep sounds
	if (IsMoving() && GetCharacterMovement()->IsMovingOnGround())
	{
		FootstepTimer += DeltaTime;

		float CurrentInterval = FootstepInterval;
		if (bIsSprinting)
			CurrentInterval *= 0.7f;
		else if (bIsCrouching)
			CurrentInterval *= 1.5f;

		if (FootstepTimer >= CurrentInterval)
		{
			PlayFootstepSound();
			FootstepTimer = 0.0f;
		}
	}
	else
	{
		FootstepTimer = 0.0f;
	}
}

void ASpacePlayerPawn::UpdateAnimations()
{
	// Animation updates will be handled by Animation Blueprint
	// This method can be used to set animation parameters if needed
}

void ASpacePlayerPawn::HandleLanding()
{
	if (LandSound)
	{
		UGameplayStatics::PlaySoundAtLocation(this, LandSound, GetActorLocation());
	}
}

void ASpacePlayerPawn::PlayFootstepSound()
{
	if (FootstepSounds.Num() > 0)
	{
		int32 RandomIndex = FMath::RandRange(0, FootstepSounds.Num() - 1);
		if (FootstepSounds[RandomIndex])
		{
			UGameplayStatics::PlaySoundAtLocation(this, FootstepSounds[RandomIndex], GetActorLocation(), 0.5f);
		}
	}
}

void ASpacePlayerPawn::SetCameraView(bool bFirstPerson)
{
	bIsFirstPerson = bFirstPerson;

	// Set camera active states
	FirstPersonCamera->SetActive(bFirstPerson);
	ThirdPersonCamera->SetActive(!bFirstPerson);

	// Set mesh visibility
	if (FirstPersonMesh)
	{
		FirstPersonMesh->SetVisibility(bFirstPerson);
	}

	if (ThirdPersonMesh)
	{
		ThirdPersonMesh->SetOwnerNoSee(bFirstPerson);
	}

	// Start camera transition
	CurrentCameraTransition = 1.0f / CameraTransitionSpeed;
}

void ASpacePlayerPawn::Landed(const FHitResult& Hit)
{
	Super::Landed(Hit);

	if (!bWasGrounded)
	{
		HandleLanding();
	}

	bWasGrounded = true;
}
