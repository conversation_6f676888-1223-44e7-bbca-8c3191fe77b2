using UnrealBuildTool;

public class SpaceGame : ModuleRules
{
	public SpaceGame(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;

		PublicDependencyModuleNames.AddRange(new string[] { 
			"Core", 
			"CoreUObject", 
			"Engine", 
			"InputCore",
			"EnhancedInput",
			"UMG",
			"Slate",
			"SlateCore",
			"GameplayTasks",
			"AIModule",
			"NavigationSystem",
			"PhysicsCore"
		});

		PrivateDependencyModuleNames.AddRange(new string[] { 
			"Slate", 
			"SlateCore",
			"ToolMenus",
			"EditorStyle",
			"EditorWidgets",
			"UnrealEd",
			"PropertyEditor",
			"RenderCore",
			"DeveloperSettings",
			"EngineSettings",
			"AudioPlatformConfiguration",
			"TargetPlatform",
			"DesktopPlatform",
			"ApplicationCore"
		});

		// Uncomment if you are using online features
		// PrivateDependencyModuleNames.Add("OnlineSubsystem");

		// To include OnlineSubsystemSteam, add it to the plugins section in your uproject file with the Enabled attribute set to true
	}
}
