#include "Spacecraft.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/Engine.h"
#include "DrawDebugHelpers.h"
#include "TimerManager.h"
#include "Kismet/GameplayStatics.h"
#include "SpaceGame.h"

ASpacecraft::ASpacecraft()
{
	// Spacecraft should not be affected by gravity when flying
	if (UPrimitiveComponent* RootPrimitive = Cast<UPrimitiveComponent>(RootComponent))
	{
		RootPrimitive->SetNotifyRigidBodyCollision(true);
	}

	TargetRotation = GetActorRotation().Euler();
}

void ASpacecraft::BeginPlay()
{
	Super::BeginPlay();
	
	// Initialize landing gear
	SetLandingGear(bIsLanded);
}

void ASpacecraft::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
	
	UpdateAltitude();
}

void ASpacecraft::HandlePilotInput()
{
	Super::HandlePilotInput();

	if (!bIsPiloted) return;

	// Takeoff/Landing
	if (UGameplayStatics::GetPlayerController(this, 0)->WasInputKeyJustPressed(EKeys::SpaceBar))
	{
		if (bIsLanded)
			TakeOff();
		else if (bCanLand)
			Land();
	}

	// Vertical movement
	if (UGameplayStatics::GetPlayerController(this, 0)->IsInputKeyDown(EKeys::Q))
		MoveInput.Z = 1.0f; // Ascend
	else if (UGameplayStatics::GetPlayerController(this, 0)->IsInputKeyDown(EKeys::E))
		MoveInput.Z = -1.0f; // Descend
	else
		MoveInput.Z = 0.0f;

	// Flight controls (when flying)
	if (bIsFlying)
	{
		APlayerController* PC = UGameplayStatics::GetPlayerController(this, 0);
		
		// Pitch (mouse Y when holding shift)
		if (PC->IsInputKeyDown(EKeys::LeftShift))
		{
			float MouseY = PC->GetInputMouseDelta().Y;
			TargetRotation.Y -= MouseY * PitchSpeed * GetWorld()->GetDeltaSeconds();
			TargetRotation.Y = FMath::Clamp(TargetRotation.Y, -45.0f, 45.0f);
		}

		// Roll (A/D when not strafing)
		if (!PC->IsInputKeyDown(EKeys::Q) && !PC->IsInputKeyDown(EKeys::E))
		{
			if (PC->IsInputKeyDown(EKeys::A))
				TargetRotation.Z += RollSpeed * GetWorld()->GetDeltaSeconds();
			else if (PC->IsInputKeyDown(EKeys::D))
				TargetRotation.Z -= RollSpeed * GetWorld()->GetDeltaSeconds();
			else
				TargetRotation.Z = FMath::FInterpTo(TargetRotation.Z, 0.0f, GetWorld()->GetDeltaSeconds(), RollSpeed / 45.0f);
		}
	}
}

void ASpacecraft::UpdateMovement()
{
	if (!bIsPiloted || !bIsEngineRunning || !HasFuel()) return;

	if (bIsFlying)
	{
		UpdateFlightMovement();
	}
	else if (bIsLanded)
	{
		UpdateGroundMovement();
	}

	UpdateRotation();
}

void ASpacecraft::UpdateAltitude()
{
	FHitResult HitResult;
	FVector TraceStart = GetActorLocation();
	FVector TraceEnd = TraceStart - FVector(0, 0, MaxAltitude);

	if (GetWorld()->LineTraceSingleByChannel(HitResult, TraceStart, TraceEnd, GroundTraceChannel))
	{
		CurrentAltitude = HitResult.Distance;
	}
	else
	{
		CurrentAltitude = MaxAltitude;
	}
}

void ASpacecraft::UpdateFlightMovement()
{
	FVector CurrentVelocity = GetVelocity();

	// Apply hover force to maintain altitude
	if (bIsHovering)
	{
		float HoverError = HoverHeight - CurrentAltitude;
		FVector HoverForceVector = FVector::UpVector * HoverError * HoverForce;
		
		if (UPrimitiveComponent* RootPrimitive = Cast<UPrimitiveComponent>(RootComponent))
		{
			RootPrimitive->AddForce(HoverForceVector);
		}
	}

	// Forward/backward movement
	if (FMath::Abs(MoveInput.X) > 0.1f)
	{
		FVector ThrustDirection = GetActorForwardVector() * MoveInput.X;
		
		if (UPrimitiveComponent* RootPrimitive = Cast<UPrimitiveComponent>(RootComponent))
		{
			RootPrimitive->AddForce(ThrustDirection * ThrustForce);
		}
	}

	// Strafe movement
	if (FMath::Abs(MoveInput.Y) > 0.1f)
	{
		FVector StrafeDirection = GetActorRightVector() * MoveInput.Y;
		
		if (UPrimitiveComponent* RootPrimitive = Cast<UPrimitiveComponent>(RootComponent))
		{
			RootPrimitive->AddForce(StrafeDirection * ThrustForce * 0.5f);
		}
	}

	// Vertical movement
	if (FMath::Abs(MoveInput.Z) > 0.1f)
	{
		FVector VerticalDirection = FVector::UpVector * MoveInput.Z;
		
		if (UPrimitiveComponent* RootPrimitive = Cast<UPrimitiveComponent>(RootComponent))
		{
			RootPrimitive->AddForce(VerticalDirection * LiftForce);
		}
	}

	// Apply stabilization
	FVector Stabilization = -CurrentVelocity * StabilizationForce;
	Stabilization.Z *= 0.1f; // Less stabilization on Z axis
	
	if (UPrimitiveComponent* RootPrimitive = Cast<UPrimitiveComponent>(RootComponent))
	{
		RootPrimitive->AddForce(Stabilization);
	}

	// Limit maximum speed
	if (CurrentVelocity.Size() > MaxSpeed)
	{
		FVector LimitedVelocity = CurrentVelocity.GetSafeNormal() * MaxSpeed;
		
		if (UPrimitiveComponent* RootPrimitive = Cast<UPrimitiveComponent>(RootComponent))
		{
			RootPrimitive->SetPhysicsLinearVelocity(LimitedVelocity);
		}
	}

	CurrentSpeed = CurrentVelocity.Size();
}

void ASpacecraft::UpdateGroundMovement()
{
	// Use base class ground movement
	Super::UpdateMovement();
}

void ASpacecraft::UpdateRotation()
{
	if (bIsFlying)
	{
		// Smooth rotation towards target
		FRotator TargetQuat = FRotator::MakeFromEuler(TargetRotation);
		FRotator NewRotation = FMath::RInterpTo(GetActorRotation(), TargetQuat, GetWorld()->GetDeltaSeconds(), 2.0f);
		SetActorRotation(NewRotation);
	}
}

void ASpacecraft::TakeOff()
{
	if (!bIsLanded || !HasFuel()) return;

	bIsLanded = false;
	bIsFlying = true;
	bIsHovering = true;

	// Enable physics
	if (UPrimitiveComponent* RootPrimitive = Cast<UPrimitiveComponent>(RootComponent))
	{
		RootPrimitive->SetSimulatePhysics(true);
		RootPrimitive->SetEnableGravity(false);
	}

	// Retract landing gear
	SetLandingGear(false);

	UE_LOG(LogSpaceGame, Log, TEXT("Spacecraft taking off"));
}

void ASpacecraft::Land()
{
	if (bIsLanded || CurrentAltitude > 500.0f) return;

	bIsFlying = false;
	bIsHovering = false;

	// Start landing sequence
	StartLandingSequence();
}

void ASpacecraft::StartLandingSequence()
{
	// Extend landing gear
	SetLandingGear(true);

	// Set up timer to complete landing
	GetWorldTimerManager().SetTimer(LandingTimerHandle, [this]()
	{
		// Check if we're close to ground
		if (CurrentAltitude <= 50.0f)
		{
			// Landed
			bIsLanded = true;
			
			// Disable physics
			if (UPrimitiveComponent* RootPrimitive = Cast<UPrimitiveComponent>(RootComponent))
			{
				RootPrimitive->SetSimulatePhysics(false);
				RootPrimitive->SetEnableGravity(true);
			}

			// Reset rotation
			TargetRotation = FVector(0.0f, GetActorRotation().Yaw, 0.0f);

			GetWorldTimerManager().ClearTimer(LandingTimerHandle);
			UE_LOG(LogSpaceGame, Log, TEXT("Spacecraft landed"));
		}
		else
		{
			// Continue descending
			FVector LandingForce = FVector::DownVector * LandingSpeed;
			
			if (UPrimitiveComponent* RootPrimitive = Cast<UPrimitiveComponent>(RootComponent))
			{
				RootPrimitive->AddForce(LandingForce);
			}
		}
	}, 0.1f, true);
}

void ASpacecraft::SetLandingGear(bool bExtended)
{
	for (UStaticMeshComponent* Gear : LandingGear)
	{
		if (Gear)
		{
			Gear->SetVisibility(bExtended);
		}
	}
}

void ASpacecraft::UpdateInteractionText()
{
	if (bIsPiloted)
	{
		if (bIsLanded)
			InteractionText = TEXT("Exit Spacecraft (E) | Takeoff (Space)");
		else
			InteractionText = TEXT("Exit Spacecraft (E) | Land (Space)");
	}
	else if (!HasFuel())
	{
		InteractionText = TEXT("Spacecraft (No Fuel)");
	}
	else
	{
		InteractionText = TEXT("Enter Spacecraft");
	}
}
