#pragma once

#include "CoreMinimal.h"
#include "VehicleBase.h"
#include "Spacecraft.generated.h"

UCLASS(BlueprintType, Blueprintable)
class SPACEGAME_API ASpacecraft : public AVehicleBase
{
	GENERATED_BODY()

public:
	ASpacecraft();

protected:
	virtual void BeginPlay() override;
	virtual void Tick(float DeltaTime) override;

	// Spacecraft Settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spacecraft")
	float HoverHeight = 200.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spacecraft")
	float HoverForce = 1000.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spacecraft")
	float StabilizationForce = 500.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spacecraft")
	float LiftForce = 1500.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spacecraft")
	float MaxAltitude = 10000.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spacecraft")
	bool bCanLand = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spacecraft")
	TEnumAsByte<ECollisionChannel> GroundTraceChannel = ECC_WorldStatic;

	// Flight Controls
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flight Controls")
	float PitchSpeed = 45.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flight Controls")
	float RollSpeed = 30.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flight Controls")
	float YawSpeed = 60.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flight Controls")
	float ThrustForce = 2000.0f;

	// Landing
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landing")
	TArray<UStaticMeshComponent*> LandingGear;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landing")
	float LandingSpeed = 200.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Landing")
	float GroundCheckDistance = 300.0f;

public:
	// Flight State
	UFUNCTION(BlueprintCallable, Category = "Spacecraft")
	void TakeOff();

	UFUNCTION(BlueprintCallable, Category = "Spacecraft")
	void Land();

	// Getters
	UFUNCTION(BlueprintPure, Category = "Spacecraft")
	bool IsFlying() const { return bIsFlying; }

	UFUNCTION(BlueprintPure, Category = "Spacecraft")
	bool IsLanded() const { return bIsLanded; }

	UFUNCTION(BlueprintPure, Category = "Spacecraft")
	float GetCurrentAltitude() const { return CurrentAltitude; }

protected:
	// Flight state
	UPROPERTY(BlueprintReadOnly, Category = "Spacecraft")
	bool bIsFlying = false;

	UPROPERTY(BlueprintReadOnly, Category = "Spacecraft")
	bool bIsLanded = true;

	UPROPERTY(BlueprintReadOnly, Category = "Spacecraft")
	bool bIsHovering = false;

	UPROPERTY(BlueprintReadOnly, Category = "Spacecraft")
	float CurrentAltitude = 0.0f;

	FVector TargetRotation = FVector::ZeroVector;

	// Override base class methods
	virtual void HandlePilotInput() override;
	virtual void UpdateMovement() override;
	virtual void UpdateInteractionText() override;

private:
	void UpdateAltitude();
	void UpdateFlightMovement();
	void UpdateGroundMovement();
	void UpdateRotation();
	void SetLandingGear(bool bExtended);

	UFUNCTION()
	void StartLandingSequence();

	FTimerHandle LandingTimerHandle;
};
