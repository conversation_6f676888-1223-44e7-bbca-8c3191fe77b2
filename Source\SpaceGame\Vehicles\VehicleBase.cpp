#include "VehicleBase.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "Camera/CameraComponent.h"
#include "GameFramework/SpringArmComponent.h"
#include "Components/AudioComponent.h"
#include "Components/InputComponent.h"
#include "GameFramework/PlayerController.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/Engine.h"
#include "SpaceGame.h"

AVehicleBase::AVehicleBase()
{
	PrimaryActorTick.bCanEverTick = true;

	InitializeComponents();
}

void AVehicleBase::InitializeComponents()
{
	// Root collision component
	CollisionComponent = CreateDefaultSubobject<UBoxComponent>(TEXT("CollisionComponent"));
	RootComponent = CollisionComponent;
	CollisionComponent->SetBoxExtent(FVector(200.0f, 100.0f, 50.0f));
	CollisionComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
	CollisionComponent->SetCollisionResponseToAllChannels(ECR_Block);

	// Vehicle mesh
	VehicleMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("VehicleMesh"));
	VehicleMesh->SetupAttachment(RootComponent);
	VehicleMesh->SetCollisionEnabled(ECollisionEnabled::NoCollision);

	// Spring arm for camera
	SpringArmComponent = CreateDefaultSubobject<USpringArmComponent>(TEXT("SpringArmComponent"));
	SpringArmComponent->SetupAttachment(RootComponent);
	SpringArmComponent->TargetArmLength = 600.0f;
	SpringArmComponent->bUsePawnControlRotation = true;
	SpringArmComponent->bInheritPitch = true;
	SpringArmComponent->bInheritYaw = true;
	SpringArmComponent->bInheritRoll = false;

	// Camera
	CameraComponent = CreateDefaultSubobject<UCameraComponent>(TEXT("CameraComponent"));
	CameraComponent->SetupAttachment(SpringArmComponent, USpringArmComponent::SocketName);

	// Engine audio
	EngineAudioComponent = CreateDefaultSubobject<UAudioComponent>(TEXT("EngineAudioComponent"));
	EngineAudioComponent->SetupAttachment(RootComponent);
	EngineAudioComponent->SetAutoActivate(false);

	// Initialize fuel
	CurrentFuel = MaxFuel;
}

void AVehicleBase::BeginPlay()
{
	Super::BeginPlay();
	
	UpdateInteractionText();
}

void AVehicleBase::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	if (bIsPiloted)
	{
		HandlePilotInput();
		UpdateMovement();
		UpdateFuel(DeltaTime);
	}

	UpdateAudio();
	UpdateInteractionText();
}

void AVehicleBase::SetupPlayerInputComponent(UInputComponent* PlayerInputComponent)
{
	Super::SetupPlayerInputComponent(PlayerInputComponent);

	// Bind movement inputs
	PlayerInputComponent->BindAxis("Move Forward / Backward", this, &AVehicleBase::MoveForward);
	PlayerInputComponent->BindAxis("Move Right / Left", this, &AVehicleBase::MoveRight);
	PlayerInputComponent->BindAxis("Turn Right / Left Mouse", this, &AVehicleBase::Turn);

	// Bind action inputs
	PlayerInputComponent->BindAction("Jump", IE_Pressed, this, &AVehicleBase::ToggleEngine);
	PlayerInputComponent->BindAction("Interact", IE_Pressed, this, &AVehicleBase::ExitVehicleInput);
}

void AVehicleBase::EnterVehicle(APawn* Pilot)
{
	if (!Pilot || bIsPiloted) return;

	CurrentPilot = Pilot;
	bIsPiloted = true;

	// Possess this vehicle
	if (APlayerController* PC = Cast<APlayerController>(Pilot->GetController()))
	{
		PC->Possess(this);
	}

	// Hide the pilot
	Pilot->SetActorHiddenInGame(true);
	Pilot->SetActorEnableCollision(false);

	UE_LOG(LogSpaceGame, Log, TEXT("Vehicle entered by %s"), *Pilot->GetName());
}

void AVehicleBase::ExitVehicle()
{
	if (!bIsPiloted || !CurrentPilot) return;

	// Stop engine when exiting
	StopEngine();

	// Find exit position
	FVector ExitLocation = GetActorLocation() + GetActorRightVector() * 300.0f;
	
	// Trace to find ground
	FHitResult HitResult;
	FVector TraceStart = ExitLocation + FVector(0, 0, 100);
	FVector TraceEnd = ExitLocation - FVector(0, 0, 500);
	
	if (GetWorld()->LineTraceSingleByChannel(HitResult, TraceStart, TraceEnd, ECC_WorldStatic))
	{
		ExitLocation = HitResult.Location;
	}

	// Restore pilot
	CurrentPilot->SetActorLocation(ExitLocation);
	CurrentPilot->SetActorHiddenInGame(false);
	CurrentPilot->SetActorEnableCollision(true);

	// Repossess the pilot
	if (APlayerController* PC = Cast<APlayerController>(GetController()))
	{
		PC->Possess(CurrentPilot);
	}

	CurrentPilot = nullptr;
	bIsPiloted = false;

	UE_LOG(LogSpaceGame, Log, TEXT("Vehicle exited"));
}

void AVehicleBase::StartEngine()
{
	if (bIsEngineRunning || !HasFuel()) return;

	bIsEngineRunning = true;

	// Play engine start sound
	if (EngineStartSound)
	{
		UGameplayStatics::PlaySoundAtLocation(this, EngineStartSound, GetActorLocation());
	}

	UE_LOG(LogSpaceGame, Log, TEXT("Engine started"));
}

void AVehicleBase::StopEngine()
{
	if (!bIsEngineRunning) return;

	bIsEngineRunning = false;

	// Play engine stop sound
	if (EngineStopSound)
	{
		UGameplayStatics::PlaySoundAtLocation(this, EngineStopSound, GetActorLocation());
	}

	UE_LOG(LogSpaceGame, Log, TEXT("Engine stopped"));
}

void AVehicleBase::AddFuel(float Amount)
{
	CurrentFuel = FMath::Clamp(CurrentFuel + Amount, 0.0f, MaxFuel);
}

void AVehicleBase::HandlePilotInput()
{
	// This will be overridden in derived classes
}

void AVehicleBase::UpdateMovement()
{
	// Basic movement implementation - override in derived classes
	if (!bIsEngineRunning || !HasFuel()) return;

	FVector ForwardVector = GetActorForwardVector();
	FVector RightVector = GetActorRightVector();

	// Calculate movement
	FVector MovementVector = (ForwardVector * MoveInput.X + RightVector * MoveInput.Y).GetSafeNormal();
	
	if (MovementVector.SizeSquared() > 0.1f)
	{
		CurrentSpeed = FMath::FInterpTo(CurrentSpeed, MaxSpeed, GetWorld()->GetDeltaSeconds(), Acceleration / MaxSpeed);
	}
	else
	{
		CurrentSpeed = FMath::FInterpTo(CurrentSpeed, 0.0f, GetWorld()->GetDeltaSeconds(), Deceleration / MaxSpeed);
	}

	// Apply movement
	FVector NewLocation = GetActorLocation() + MovementVector * CurrentSpeed * GetWorld()->GetDeltaSeconds();
	SetActorLocation(NewLocation);

	// Apply rotation
	if (FMath::Abs(TurnInput) > 0.1f)
	{
		FRotator NewRotation = GetActorRotation();
		NewRotation.Yaw += TurnInput * TurnSpeed * GetWorld()->GetDeltaSeconds();
		SetActorRotation(NewRotation);
	}
}

void AVehicleBase::UpdateAudio()
{
	if (!EngineAudioComponent) return;

	if (bIsEngineRunning && HasFuel())
	{
		if (CurrentSpeed > 10.0f && EngineRunningSound)
		{
			if (EngineAudioComponent->Sound != EngineRunningSound)
			{
				EngineAudioComponent->SetSound(EngineRunningSound);
				EngineAudioComponent->Play();
			}
		}
		else if (EngineIdleSound)
		{
			if (EngineAudioComponent->Sound != EngineIdleSound)
			{
				EngineAudioComponent->SetSound(EngineIdleSound);
				EngineAudioComponent->Play();
			}
		}
	}
	else
	{
		if (EngineAudioComponent->IsPlaying())
		{
			EngineAudioComponent->Stop();
		}
	}
}

void AVehicleBase::UpdateFuel(float DeltaTime)
{
	if (bIsEngineRunning && CurrentSpeed > 0.0f)
	{
		CurrentFuel = FMath::Max(0.0f, CurrentFuel - FuelConsumptionRate * DeltaTime);
		
		if (CurrentFuel <= 0.0f)
		{
			StopEngine();
		}
	}
}

void AVehicleBase::UpdateInteractionText()
{
	if (bIsPiloted)
	{
		InteractionText = TEXT("Exit Vehicle (E)");
	}
	else if (!HasFuel())
	{
		InteractionText = TEXT("Vehicle (No Fuel)");
	}
	else
	{
		InteractionText = TEXT("Enter Vehicle");
	}
}

void AVehicleBase::MoveForward(float Value)
{
	MoveInput.X = Value;
}

void AVehicleBase::MoveRight(float Value)
{
	MoveInput.Y = Value;
}

void AVehicleBase::Turn(float Value)
{
	TurnInput = Value;
}

void AVehicleBase::ToggleEngine()
{
	if (bIsEngineRunning)
	{
		StopEngine();
	}
	else
	{
		StartEngine();
	}
}

void AVehicleBase::ExitVehicleInput()
{
	if (bIsPiloted)
	{
		ExitVehicle();
	}
}

void AVehicleBase::Interact_Implementation(AActor* InteractingActor)
{
	if (APawn* InteractingPawn = Cast<APawn>(InteractingActor))
	{
		if (bIsPiloted)
		{
			ExitVehicle();
		}
		else
		{
			EnterVehicle(InteractingPawn);
		}
	}
}

bool AVehicleBase::CanInteract_Implementation(AActor* InteractingActor) const
{
	return Cast<APawn>(InteractingActor) != nullptr;
}

FString AVehicleBase::GetInteractionText_Implementation(AActor* InteractingActor) const
{
	return InteractionText;
}
