#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Pawn.h"
#include "Core/InteractableInterface.h"
#include "VehicleBase.generated.h"

class UStaticMeshComponent;
class UBoxComponent;
class UCameraComponent;
class USpringArmComponent;
class UAudioComponent;

UCLASS(Abstract, BlueprintType, Blueprintable)
class SPACEGAME_API AVehicleBase : public APawn, public IInteractableInterface
{
	GENERATED_BODY()

public:
	AVehicleBase();

protected:
	virtual void BeginPlay() override;
	virtual void Tick(float DeltaTime) override;
	virtual void SetupPlayerInputComponent(UInputComponent* PlayerInputComponent) override;

	// Components
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UStaticMeshComponent* VehicleMesh;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UBoxComponent* CollisionComponent;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	USpringArmComponent* SpringArmComponent;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UCameraComponent* CameraComponent;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UAudioComponent* EngineAudioComponent;

	// Vehicle Settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vehicle")
	float MaxSpeed = 1000.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vehicle")
	float Acceleration = 500.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vehicle")
	float Deceleration = 1000.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vehicle")
	float TurnSpeed = 90.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vehicle")
	float MaxFuel = 100.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vehicle")
	float FuelConsumptionRate = 1.0f;

	// Audio
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
	USoundBase* EngineStartSound;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
	USoundBase* EngineStopSound;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
	USoundBase* EngineIdleSound;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
	USoundBase* EngineRunningSound;

public:
	// Vehicle Control
	UFUNCTION(BlueprintCallable, Category = "Vehicle")
	virtual void EnterVehicle(APawn* Pilot);

	UFUNCTION(BlueprintCallable, Category = "Vehicle")
	virtual void ExitVehicle();

	UFUNCTION(BlueprintCallable, Category = "Vehicle")
	virtual void StartEngine();

	UFUNCTION(BlueprintCallable, Category = "Vehicle")
	virtual void StopEngine();

	UFUNCTION(BlueprintCallable, Category = "Vehicle")
	void AddFuel(float Amount);

	// Getters
	UFUNCTION(BlueprintPure, Category = "Vehicle")
	bool IsPiloted() const { return bIsPiloted; }

	UFUNCTION(BlueprintPure, Category = "Vehicle")
	bool IsEngineRunning() const { return bIsEngineRunning; }

	UFUNCTION(BlueprintPure, Category = "Vehicle")
	bool HasFuel() const { return CurrentFuel > 0.0f; }

	UFUNCTION(BlueprintPure, Category = "Vehicle")
	float GetCurrentSpeed() const { return CurrentSpeed; }

	UFUNCTION(BlueprintPure, Category = "Vehicle")
	float GetFuelPercentage() const { return CurrentFuel / MaxFuel; }

	UFUNCTION(BlueprintPure, Category = "Vehicle")
	APawn* GetPilot() const { return CurrentPilot; }

	// IInteractableInterface
	virtual void Interact_Implementation(AActor* InteractingActor) override;
	virtual bool CanInteract_Implementation(AActor* InteractingActor) const override;
	virtual FString GetInteractionText_Implementation(AActor* InteractingActor) const override;

protected:
	// Vehicle State
	UPROPERTY(BlueprintReadOnly, Category = "Vehicle")
	bool bIsPiloted = false;

	UPROPERTY(BlueprintReadOnly, Category = "Vehicle")
	bool bIsEngineRunning = false;

	UPROPERTY(BlueprintReadOnly, Category = "Vehicle")
	float CurrentSpeed = 0.0f;

	UPROPERTY(BlueprintReadOnly, Category = "Vehicle")
	float CurrentFuel = 100.0f;

	UPROPERTY(BlueprintReadOnly, Category = "Vehicle")
	APawn* CurrentPilot = nullptr;

	// Input
	FVector MoveInput = FVector::ZeroVector;
	float TurnInput = 0.0f;

	// Virtual functions for derived classes
	virtual void HandlePilotInput();
	virtual void UpdateMovement();
	virtual void UpdateAudio();
	virtual void UpdateFuel(float DeltaTime);
	virtual void UpdateInteractionText();

	// Input Functions
	void MoveForward(float Value);
	void MoveRight(float Value);
	void Turn(float Value);
	void ToggleEngine();
	void ExitVehicleInput();

private:
	void InitializeComponents();
	FString InteractionText = TEXT("Enter Vehicle");
};
